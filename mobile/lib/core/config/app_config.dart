class AppConfig {
  static const String appName = 'PhysioCenter';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Comprehensive physiotherapy center management';
  
  // API Configuration
  static const String baseUrl = 'http://localhost:3000/api';
  static const String websocketUrl = 'ws://localhost:3000';
  
  // Firebase Configuration
  static const String firebaseProjectId = 'physio-center-app';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String onboardingKey = 'onboarding_completed';
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Cache Duration
  static const Duration cacheExpiry = Duration(minutes: 30);
  static const Duration tokenRefreshThreshold = Duration(minutes: 5);
  
  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];
  
  // Subscription Plans
  static const Map<String, Map<String, dynamic>> subscriptionPlans = {
    'TRIAL': {
      'name': 'Trial',
      'price': 0,
      'duration': 30,
      'features': {
        'maxPatients': 10,
        'maxTherapists': 2,
        'maxEmployees': 3,
        'hrmModule': false,
        'accountingModule': false,
        'multiBranch': false,
      }
    },
    'BASIC': {
      'name': 'Basic',
      'price': 1500,
      'duration': null,
      'features': {
        'maxPatients': 100,
        'maxTherapists': 5,
        'maxEmployees': 10,
        'hrmModule': true,
        'accountingModule': false,
        'multiBranch': false,
      }
    },
    'PRO': {
      'name': 'Pro',
      'price': 3000,
      'duration': null,
      'features': {
        'maxPatients': 500,
        'maxTherapists': 20,
        'maxEmployees': 50,
        'hrmModule': true,
        'accountingModule': true,
        'multiBranch': true,
      }
    },
    'ENTERPRISE': {
      'name': 'Enterprise',
      'price': 5000,
      'duration': null,
      'features': {
        'maxPatients': -1,
        'maxTherapists': -1,
        'maxEmployees': -1,
        'hrmModule': true,
        'accountingModule': true,
        'multiBranch': true,
      }
    },
  };
  
  // User Roles
  static const List<String> userRoles = [
    'SUPER_ADMIN',
    'CENTER_OWNER',
    'THERAPIST',
    'PATIENT',
  ];
  
  // Session Types
  static const List<String> sessionTypes = [
    'CONSULTATION',
    'THERAPY',
    'FOLLOW_UP',
  ];
  
  // Session Status
  static const List<String> sessionStatus = [
    'SCHEDULED',
    'IN_PROGRESS',
    'COMPLETED',
    'CANCELLED',
    'NO_SHOW',
  ];
  
  // Employee Status
  static const List<String> employeeStatus = [
    'ACTIVE',
    'INACTIVE',
    'TERMINATED',
    'ON_LEAVE',
  ];
  
  // Expense Categories
  static const List<String> expenseCategories = [
    'OFFICE_SUPPLIES',
    'EQUIPMENT',
    'UTILITIES',
    'RENT',
    'MARKETING',
    'TRAVEL',
    'MEALS',
    'PROFESSIONAL_SERVICES',
    'INSURANCE',
    'MAINTENANCE',
    'OTHER',
  ];
  
  // Payment Methods
  static const List<String> paymentMethods = [
    'CASH',
    'BANK_TRANSFER',
    'CREDIT_CARD',
    'DEBIT_CARD',
    'MOBILE_BANKING',
    'CHECK',
    'OTHER',
  ];
  
  // Notification Types
  static const List<String> notificationTypes = [
    'REMINDER',
    'APPOINTMENT',
    'PAYMENT',
    'SYSTEM',
  ];
  
  // Environment Detection
  static bool get isDebug {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }
  
  static bool get isProduction => !isDebug;
  
  // Feature Flags
  static const Map<String, bool> featureFlags = {
    'enableBiometricAuth': true,
    'enableOfflineMode': true,
    'enablePushNotifications': true,
    'enableAnalytics': true,
    'enableCrashReporting': true,
    'enableDeepLinking': true,
  };
  
  // App URLs
  static const String privacyPolicyUrl = 'https://physiocenter.com/privacy';
  static const String termsOfServiceUrl = 'https://physiocenter.com/terms';
  static const String supportUrl = 'https://physiocenter.com/support';
  static const String websiteUrl = 'https://physiocenter.com';
  
  // Contact Information
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+880-1234-567890';
  
  // Social Media
  static const Map<String, String> socialMedia = {
    'facebook': 'https://facebook.com/physiocenter',
    'twitter': 'https://twitter.com/physiocenter',
    'linkedin': 'https://linkedin.com/company/physiocenter',
    'instagram': 'https://instagram.com/physiocenter',
  };
}
