import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../config/app_config.dart';
import 'storage_service.dart';
import 'auth_service.dart';

class ApiResponse<T> {
  final bool isSuccess;
  final T? data;
  final String? error;
  final int? statusCode;

  ApiResponse({
    required this.isSuccess,
    this.data,
    this.error,
    this.statusCode,
  });

  factory ApiResponse.success(T data, [int? statusCode]) {
    return ApiResponse(
      isSuccess: true,
      data: data,
      statusCode: statusCode,
    );
  }

  factory ApiResponse.error(String error, [int? statusCode]) {
    return ApiResponse(
      isSuccess: false,
      error: error,
      statusCode: statusCode,
    );
  }
}

class ApiService {
  static late Dio _dio;
  static final Logger _logger = Logger();
  static final Connectivity _connectivity = Connectivity();

  // Initialize API service
  static void init() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(_AuthInterceptor());
    _dio.interceptors.add(_LoggingInterceptor());
    _dio.interceptors.add(_ErrorInterceptor());

    _logger.i('API service initialized with base URL: ${AppConfig.baseUrl}');
  }

  // Check internet connectivity
  static Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      _logger.e('Error checking connectivity: $e');
      return false;
    }
  }

  // GET request
  static Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        return ApiResponse.error('No internet connection');
      }

      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
        options: options,
      );

      return ApiResponse.success(response.data, response.statusCode);
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      _logger.e('GET request error: $e');
      return ApiResponse.error('Unexpected error occurred');
    }
  }

  // POST request
  static Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        return ApiResponse.error('No internet connection');
      }

      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      return ApiResponse.success(response.data, response.statusCode);
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      _logger.e('POST request error: $e');
      return ApiResponse.error('Unexpected error occurred');
    }
  }

  // PUT request
  static Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        return ApiResponse.error('No internet connection');
      }

      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      return ApiResponse.success(response.data, response.statusCode);
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      _logger.e('PUT request error: $e');
      return ApiResponse.error('Unexpected error occurred');
    }
  }

  // DELETE request
  static Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      if (!await hasInternetConnection()) {
        return ApiResponse.error('No internet connection');
      }

      final response = await _dio.delete(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      return ApiResponse.success(response.data, response.statusCode);
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      _logger.e('DELETE request error: $e');
      return ApiResponse.error('Unexpected error occurred');
    }
  }

  // Handle Dio errors
  static ApiResponse<T> _handleDioError<T>(DioException error) {
    String errorMessage;
    int? statusCode = error.response?.statusCode;

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        errorMessage = 'Connection timeout';
        break;
      case DioExceptionType.sendTimeout:
        errorMessage = 'Send timeout';
        break;
      case DioExceptionType.receiveTimeout:
        errorMessage = 'Receive timeout';
        break;
      case DioExceptionType.badResponse:
        errorMessage = _extractErrorMessage(error.response?.data) ?? 
                     'Server error (${error.response?.statusCode})';
        break;
      case DioExceptionType.cancel:
        errorMessage = 'Request cancelled';
        break;
      case DioExceptionType.connectionError:
        errorMessage = 'Connection error';
        break;
      default:
        errorMessage = 'Network error occurred';
    }

    _logger.e('API Error: $errorMessage (Status: $statusCode)');
    return ApiResponse.error(errorMessage, statusCode);
  }

  // Extract error message from response
  static String? _extractErrorMessage(dynamic responseData) {
    if (responseData is Map<String, dynamic>) {
      return responseData['message'] ?? 
             responseData['error'] ?? 
             responseData['detail'];
    }
    return null;
  }
}

// Auth interceptor to add authorization header
class _AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await AuthService.getCurrentUserToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Handle token refresh on 401 errors
    if (err.response?.statusCode == 401) {
      final newToken = await AuthService.refreshToken();
      if (newToken != null) {
        // Retry the request with new token
        final options = err.requestOptions;
        options.headers['Authorization'] = 'Bearer $newToken';
        
        try {
          final response = await Dio().fetch(options);
          handler.resolve(response);
          return;
        } catch (e) {
          // If retry fails, proceed with original error
        }
      }
    }
    handler.next(err);
  }
}

// Logging interceptor
class _LoggingInterceptor extends Interceptor {
  final Logger _logger = Logger();

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (AppConfig.isDebug) {
      _logger.d('API Request: ${options.method} ${options.uri}');
      if (options.data != null) {
        _logger.d('Request Data: ${options.data}');
      }
    }
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (AppConfig.isDebug) {
      _logger.d('API Response: ${response.statusCode} ${response.requestOptions.uri}');
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (AppConfig.isDebug) {
      _logger.e('API Error: ${err.message}');
      if (err.response != null) {
        _logger.e('Error Response: ${err.response?.data}');
      }
    }
    handler.next(err);
  }
}

// Error interceptor for global error handling
class _ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Handle specific error codes globally
    switch (err.response?.statusCode) {
      case 401:
        // Unauthorized - redirect to login
        break;
      case 403:
        // Forbidden - show access denied message
        break;
      case 500:
        // Server error - show generic error message
        break;
    }
    handler.next(err);
  }
}
