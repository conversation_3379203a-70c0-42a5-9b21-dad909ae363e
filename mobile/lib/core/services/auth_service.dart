import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../config/app_config.dart';
import 'storage_service.dart';
import 'api_service.dart';
import '../../shared/models/user_model.dart';

class AuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final GoogleSignIn _googleSignIn = GoogleSignIn();
  static final Logger _logger = Logger();

  // Get current user
  static User? get currentUser => _auth.currentUser;
  
  // Get auth state stream
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with Google
  static Future<UserCredential?> signInWithGoogle({
    required String role,
  }) async {
    try {
      _logger.i('Starting Google Sign-In process for role: $role');
      
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        _logger.w('Google Sign-In cancelled by user');
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final UserCredential userCredential = await _auth.signInWithCredential(credential);
      
      if (userCredential.user != null) {
        // Get Firebase ID token
        final String? idToken = await userCredential.user!.getIdToken();
        
        if (idToken != null) {
          // Send to backend for user creation/login
          final response = await ApiService.post('/auth/login', {
            'idToken': idToken,
            'role': role,
          });
          
          if (response.isSuccess) {
            // Store user data locally
            final userData = response.data['user'];
            await StorageService.setString(AppConfig.userDataKey, userData.toString());
            await StorageService.setString(AppConfig.userTokenKey, idToken);
            
            _logger.i('User signed in successfully: ${userCredential.user!.email}');
            return userCredential;
          } else {
            _logger.e('Backend authentication failed: ${response.error}');
            await signOut();
            throw Exception('Authentication failed: ${response.error}');
          }
        }
      }
      
      return userCredential;
    } catch (e) {
      _logger.e('Google Sign-In error: $e');
      rethrow;
    }
  }

  // Sign out
  static Future<void> signOut() async {
    try {
      _logger.i('Signing out user');
      
      // Sign out from Google
      await _googleSignIn.signOut();
      
      // Sign out from Firebase
      await _auth.signOut();
      
      // Clear local storage
      await StorageService.remove(AppConfig.userDataKey);
      await StorageService.remove(AppConfig.userTokenKey);
      
      _logger.i('User signed out successfully');
    } catch (e) {
      _logger.e('Sign out error: $e');
      rethrow;
    }
  }

  // Get current user token
  static Future<String?> getCurrentUserToken() async {
    try {
      final user = currentUser;
      if (user != null) {
        return await user.getIdToken();
      }
      return null;
    } catch (e) {
      _logger.e('Error getting user token: $e');
      return null;
    }
  }

  // Refresh token
  static Future<String?> refreshToken() async {
    try {
      final user = currentUser;
      if (user != null) {
        await user.reload();
        final token = await user.getIdToken(true); // Force refresh
        await StorageService.setString(AppConfig.userTokenKey, token);
        return token;
      }
      return null;
    } catch (e) {
      _logger.e('Error refreshing token: $e');
      return null;
    }
  }

  // Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  // Get stored user data
  static Future<UserModel?> getStoredUserData() async {
    try {
      final userDataString = await StorageService.getString(AppConfig.userDataKey);
      if (userDataString != null) {
        // Parse and return user data
        // This would need proper JSON parsing based on your UserModel
        return UserModel.fromJson(userDataString);
      }
      return null;
    } catch (e) {
      _logger.e('Error getting stored user data: $e');
      return null;
    }
  }

  // Update FCM token
  static Future<void> updateFCMToken(String fcmToken) async {
    try {
      final token = await getCurrentUserToken();
      if (token != null) {
        await ApiService.post('/auth/update-fcm-token', {
          'fcmToken': fcmToken,
        });
        _logger.i('FCM token updated successfully');
      }
    } catch (e) {
      _logger.e('Error updating FCM token: $e');
    }
  }

  // Delete account
  static Future<void> deleteAccount() async {
    try {
      _logger.i('Deleting user account');
      
      final user = currentUser;
      if (user != null) {
        // Delete from backend first
        await ApiService.delete('/auth/delete-account');
        
        // Delete from Firebase
        await user.delete();
        
        // Clear local storage
        await StorageService.remove(AppConfig.userDataKey);
        await StorageService.remove(AppConfig.userTokenKey);
        
        _logger.i('Account deleted successfully');
      }
    } catch (e) {
      _logger.e('Error deleting account: $e');
      rethrow;
    }
  }

  // Re-authenticate user (for sensitive operations)
  static Future<bool> reauthenticate() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) return false;

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final user = currentUser;
      if (user != null) {
        await user.reauthenticateWithCredential(credential);
        return true;
      }
      
      return false;
    } catch (e) {
      _logger.e('Re-authentication error: $e');
      return false;
    }
  }
}

// Riverpod providers for auth state management
final authServiceProvider = Provider<AuthService>((ref) => AuthService());

final authStateProvider = StreamProvider<User?>((ref) {
  return AuthService.authStateChanges;
});

final currentUserProvider = Provider<User?>((ref) {
  return AuthService.currentUser;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user != null;
});
