import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

import '../config/app_config.dart';
import 'storage_service.dart';
import 'auth_service.dart';

class NotificationService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();
  static final Logger _logger = Logger();

  // Initialize notification service
  static Future<void> init() async {
    try {
      // Request notification permissions
      await _requestPermissions();
      
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();
      
      _logger.i('Notification service initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize notification service: $e');
    }
  }

  // Request notification permissions
  static Future<void> _requestPermissions() async {
    // Request notification permission
    final notificationStatus = await Permission.notification.request();
    _logger.i('Notification permission status: $notificationStatus');

    // Request Firebase messaging permission
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    _logger.i('Firebase messaging permission status: ${settings.authorizationStatus}');
  }

  // Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels for Android
    await _createNotificationChannels();
  }

  // Initialize Firebase messaging
  static Future<void> _initializeFirebaseMessaging() async {
    // Get FCM token
    final token = await _firebaseMessaging.getToken();
    if (token != null) {
      _logger.i('FCM Token: $token');
      await StorageService.setString('fcm_token', token);
      
      // Update token on backend if user is authenticated
      if (AuthService.isAuthenticated) {
        await AuthService.updateFCMToken(token);
      }
    }

    // Listen for token refresh
    _firebaseMessaging.onTokenRefresh.listen((token) async {
      _logger.i('FCM Token refreshed: $token');
      await StorageService.setString('fcm_token', token);
      
      if (AuthService.isAuthenticated) {
        await AuthService.updateFCMToken(token);
      }
    });

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }
  }

  // Create notification channels for Android
  static Future<void> _createNotificationChannels() async {
    const channels = [
      AndroidNotificationChannel(
        'appointments',
        'Appointments',
        description: 'Notifications for appointments and sessions',
        importance: Importance.high,
        sound: RawResourceAndroidNotificationSound('notification'),
      ),
      AndroidNotificationChannel(
        'reminders',
        'Reminders',
        description: 'General reminders and alerts',
        importance: Importance.defaultImportance,
      ),
      AndroidNotificationChannel(
        'system',
        'System',
        description: 'System notifications and updates',
        importance: Importance.low,
      ),
    ];

    for (final channel in channels) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }
  }

  // Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    _logger.i('Received foreground message: ${message.messageId}');
    
    // Show local notification when app is in foreground
    await _showLocalNotification(message);
  }

  // Handle background messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    _logger.i('Received background message: ${message.messageId}');
    // Background messages are automatically displayed by the system
  }

  // Handle notification tap
  static void _handleNotificationTap(RemoteMessage message) {
    _logger.i('Notification tapped: ${message.messageId}');
    
    // Navigate to appropriate screen based on notification data
    final data = message.data;
    _navigateBasedOnNotification(data);
  }

  // Handle local notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    _logger.i('Local notification tapped: ${response.id}');
    
    // Parse payload and navigate
    if (response.payload != null) {
      // Parse JSON payload and navigate accordingly
      _navigateBasedOnPayload(response.payload!);
    }
  }

  // Show local notification
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      final channelId = _getChannelId(data['type'] ?? 'system');
      
      await _localNotifications.show(
        message.hashCode,
        notification.title,
        notification.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            channelId,
            _getChannelName(channelId),
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
          ),
          iOS: const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        payload: message.data.isNotEmpty ? message.data.toString() : null,
      );
    }
  }

  // Get notification channel ID based on type
  static String _getChannelId(String type) {
    switch (type.toLowerCase()) {
      case 'appointment':
      case 'session':
        return 'appointments';
      case 'reminder':
        return 'reminders';
      default:
        return 'system';
    }
  }

  // Get channel name
  static String _getChannelName(String channelId) {
    switch (channelId) {
      case 'appointments':
        return 'Appointments';
      case 'reminders':
        return 'Reminders';
      default:
        return 'System';
    }
  }

  // Navigate based on notification data
  static void _navigateBasedOnNotification(Map<String, dynamic> data) {
    final type = data['type'];
    final id = data['id'];

    switch (type) {
      case 'appointment':
      case 'session':
        // Navigate to session details
        _logger.i('Navigating to session: $id');
        break;
      case 'patient':
        // Navigate to patient details
        _logger.i('Navigating to patient: $id');
        break;
      case 'payment':
        // Navigate to payment/billing
        _logger.i('Navigating to payment: $id');
        break;
      default:
        // Navigate to notifications list
        _logger.i('Navigating to notifications');
        break;
    }
  }

  // Navigate based on payload
  static void _navigateBasedOnPayload(String payload) {
    try {
      // Parse payload and navigate
      _logger.i('Processing notification payload: $payload');
      // Implementation depends on your navigation structure
    } catch (e) {
      _logger.e('Error processing notification payload: $e');
    }
  }

  // Schedule local notification
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    String channelId = 'reminders',
  }) async {
    try {
      await _localNotifications.zonedSchedule(
        id,
        title,
        body,
        scheduledDate,
        NotificationDetails(
          android: AndroidNotificationDetails(
            channelId,
            _getChannelName(channelId),
            importance: Importance.high,
            priority: Priority.high,
          ),
          iOS: const DarwinNotificationDetails(),
        ),
        payload: payload,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      );
      
      _logger.i('Scheduled notification: $title for $scheduledDate');
    } catch (e) {
      _logger.e('Error scheduling notification: $e');
    }
  }

  // Cancel notification
  static Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
    _logger.i('Cancelled notification: $id');
  }

  // Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
    _logger.i('Cancelled all notifications');
  }

  // Get FCM token
  static Future<String?> getFCMToken() async {
    try {
      return await _firebaseMessaging.getToken();
    } catch (e) {
      _logger.e('Error getting FCM token: $e');
      return null;
    }
  }

  // Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      _logger.i('Subscribed to topic: $topic');
    } catch (e) {
      _logger.e('Error subscribing to topic $topic: $e');
    }
  }

  // Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      _logger.i('Unsubscribed from topic: $topic');
    } catch (e) {
      _logger.e('Error unsubscribing from topic $topic: $e');
    }
  }
}
