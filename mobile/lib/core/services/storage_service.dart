import 'package:hive_flutter/hive_flutter.dart';
import 'package:logger/logger.dart';

class StorageService {
  static late Box _box;
  static final Logger _logger = Logger();
  
  static const String _boxName = 'physio_center_storage';

  // Initialize storage
  static Future<void> init() async {
    try {
      await Hive.initFlutter();
      _box = await Hive.openBox(_boxName);
      _logger.i('Storage service initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize storage service: $e');
      rethrow;
    }
  }

  // String operations
  static Future<void> setString(String key, String value) async {
    try {
      await _box.put(key, value);
      _logger.d('Stored string with key: $key');
    } catch (e) {
      _logger.e('Failed to store string with key $key: $e');
      rethrow;
    }
  }

  static Future<String?> getString(String key) async {
    try {
      final value = _box.get(key);
      _logger.d('Retrieved string with key: $key');
      return value?.toString();
    } catch (e) {
      _logger.e('Failed to retrieve string with key $key: $e');
      return null;
    }
  }

  // Integer operations
  static Future<void> setInt(String key, int value) async {
    try {
      await _box.put(key, value);
      _logger.d('Stored int with key: $key');
    } catch (e) {
      _logger.e('Failed to store int with key $key: $e');
      rethrow;
    }
  }

  static Future<int?> getInt(String key) async {
    try {
      final value = _box.get(key);
      _logger.d('Retrieved int with key: $key');
      return value is int ? value : null;
    } catch (e) {
      _logger.e('Failed to retrieve int with key $key: $e');
      return null;
    }
  }

  // Boolean operations
  static Future<void> setBool(String key, bool value) async {
    try {
      await _box.put(key, value);
      _logger.d('Stored bool with key: $key');
    } catch (e) {
      _logger.e('Failed to store bool with key $key: $e');
      rethrow;
    }
  }

  static Future<bool?> getBool(String key) async {
    try {
      final value = _box.get(key);
      _logger.d('Retrieved bool with key: $key');
      return value is bool ? value : null;
    } catch (e) {
      _logger.e('Failed to retrieve bool with key $key: $e');
      return null;
    }
  }

  // Double operations
  static Future<void> setDouble(String key, double value) async {
    try {
      await _box.put(key, value);
      _logger.d('Stored double with key: $key');
    } catch (e) {
      _logger.e('Failed to store double with key $key: $e');
      rethrow;
    }
  }

  static Future<double?> getDouble(String key) async {
    try {
      final value = _box.get(key);
      _logger.d('Retrieved double with key: $key');
      return value is double ? value : null;
    } catch (e) {
      _logger.e('Failed to retrieve double with key $key: $e');
      return null;
    }
  }

  // List operations
  static Future<void> setList(String key, List<dynamic> value) async {
    try {
      await _box.put(key, value);
      _logger.d('Stored list with key: $key');
    } catch (e) {
      _logger.e('Failed to store list with key $key: $e');
      rethrow;
    }
  }

  static Future<List<dynamic>?> getList(String key) async {
    try {
      final value = _box.get(key);
      _logger.d('Retrieved list with key: $key');
      return value is List ? value : null;
    } catch (e) {
      _logger.e('Failed to retrieve list with key $key: $e');
      return null;
    }
  }

  // Map operations
  static Future<void> setMap(String key, Map<String, dynamic> value) async {
    try {
      await _box.put(key, value);
      _logger.d('Stored map with key: $key');
    } catch (e) {
      _logger.e('Failed to store map with key $key: $e');
      rethrow;
    }
  }

  static Future<Map<String, dynamic>?> getMap(String key) async {
    try {
      final value = _box.get(key);
      _logger.d('Retrieved map with key: $key');
      return value is Map ? Map<String, dynamic>.from(value) : null;
    } catch (e) {
      _logger.e('Failed to retrieve map with key $key: $e');
      return null;
    }
  }

  // Generic operations
  static Future<void> set(String key, dynamic value) async {
    try {
      await _box.put(key, value);
      _logger.d('Stored value with key: $key');
    } catch (e) {
      _logger.e('Failed to store value with key $key: $e');
      rethrow;
    }
  }

  static Future<T?> get<T>(String key) async {
    try {
      final value = _box.get(key);
      _logger.d('Retrieved value with key: $key');
      return value is T ? value : null;
    } catch (e) {
      _logger.e('Failed to retrieve value with key $key: $e');
      return null;
    }
  }

  // Remove operations
  static Future<void> remove(String key) async {
    try {
      await _box.delete(key);
      _logger.d('Removed value with key: $key');
    } catch (e) {
      _logger.e('Failed to remove value with key $key: $e');
      rethrow;
    }
  }

  // Check if key exists
  static bool containsKey(String key) {
    try {
      return _box.containsKey(key);
    } catch (e) {
      _logger.e('Failed to check if key exists $key: $e');
      return false;
    }
  }

  // Get all keys
  static List<String> getAllKeys() {
    try {
      return _box.keys.cast<String>().toList();
    } catch (e) {
      _logger.e('Failed to get all keys: $e');
      return [];
    }
  }

  // Clear all data
  static Future<void> clear() async {
    try {
      await _box.clear();
      _logger.i('Cleared all storage data');
    } catch (e) {
      _logger.e('Failed to clear storage: $e');
      rethrow;
    }
  }

  // Get storage size
  static int getSize() {
    try {
      return _box.length;
    } catch (e) {
      _logger.e('Failed to get storage size: $e');
      return 0;
    }
  }

  // Close storage
  static Future<void> close() async {
    try {
      await _box.close();
      _logger.i('Storage service closed');
    } catch (e) {
      _logger.e('Failed to close storage: $e');
      rethrow;
    }
  }

  // Compact storage (optimize space)
  static Future<void> compact() async {
    try {
      await _box.compact();
      _logger.i('Storage compacted successfully');
    } catch (e) {
      _logger.e('Failed to compact storage: $e');
      rethrow;
    }
  }
}
