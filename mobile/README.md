# PhysioCenter Mobile App

A comprehensive Flutter mobile application for physiotherapy center management with role-based access, subscription features, and offline capabilities.

## 🌟 Features

### Core Features
- **Firebase Authentication** with Google Sign-In
- **Role-based Access Control** (Center Owner, Therapist, Patient, Super Admin)
- **Subscription-based Features** with plan restrictions
- **Offline Mode** with local data synchronization
- **Push Notifications** for appointments and reminders
- **Multi-language Support** (English, Bengali)
- **Dark/Light Theme** with system detection

### Patient Management
- Complete patient profiles and medical history
- Patient search and filtering
- Medical record attachments
- Progress tracking and notes

### Session Management
- Appointment scheduling and management
- Session types (Consultation, Therapy, Follow-up)
- Real-time session status updates
- Session notes and progress tracking

### HRM Module (BASIC+ plans)
- Employee management and profiles
- Attendance tracking with check-in/out
- Leave management and approval workflow
- Payroll calculation and records

### Accounting Module (PRO+ plans)
- Expense tracking and categorization
- Invoice generation and management
- Payment tracking and reports
- Financial analytics and insights

### Premium Features
- **Multi-branch Support** (PRO+ plans)
- **Advanced Analytics** (BASIC+ plans)
- **Custom Reports** (PRO+ plans)
- **API Integration** (PRO+ plans)

## 🏗️ Architecture

### State Management
- **Riverpod** for reactive state management
- **Code Generation** for providers and models
- **Immutable State** with proper data flow

### Data Layer
- **Repository Pattern** for data abstraction
- **Local Storage** with Hive for offline support
- **API Client** with Dio and Retrofit
- **Caching Strategy** for optimal performance

### UI/UX
- **Material Design 3** with custom theming
- **Responsive Design** for all screen sizes
- **Accessibility** support with semantic widgets
- **Custom Components** for consistent UI

### Security
- **Firebase Authentication** with JWT tokens
- **Biometric Authentication** (optional)
- **Secure Storage** for sensitive data
- **Certificate Pinning** for API security

## 📱 Screens & Navigation

### Authentication Flow
- **Splash Screen** with app initialization
- **Onboarding** for new users
- **Login Screen** with Google Sign-In
- **Role Selection** during registration

### Main Navigation
- **Dashboard** with role-based widgets
- **Patients** management and search
- **Sessions** calendar and scheduling
- **Profile** and settings management

### Role-specific Screens
- **Center Owner**: Full access to all features
- **Therapist**: Patient and session management
- **Patient**: View appointments and progress
- **Super Admin**: System-wide management

### Premium Screens (Subscription-gated)
- **HRM Dashboard** (BASIC+)
- **Employee Management** (BASIC+)
- **Accounting Dashboard** (PRO+)
- **Multi-branch Management** (PRO+)

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (>=3.10.0)
- Dart SDK (>=3.0.0)
- Android Studio / VS Code
- Firebase project setup
- Physical device or emulator

### Installation

1. **Clone the repository**
   ```bash
   cd mobile
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure Firebase**
   ```bash
   # Install FlutterFire CLI
   dart pub global activate flutterfire_cli
   
   # Configure Firebase for your project
   flutterfire configure
   ```

4. **Generate code**
   ```bash
   dart run build_runner build
   ```

5. **Run the app**
   ```bash
   flutter run
   ```

### Configuration

#### Environment Setup
Create `lib/core/config/env.dart`:
```dart
class Environment {
  static const String apiBaseUrl = 'http://your-api-url.com/api';
  static const String websocketUrl = 'ws://your-api-url.com';
  static const bool enableLogging = true;
}
```

#### Firebase Setup
1. Create a Firebase project
2. Enable Authentication with Google provider
3. Enable Cloud Messaging for notifications
4. Download configuration files:
   - `android/app/google-services.json`
   - `ios/Runner/GoogleService-Info.plist`

## 📦 Project Structure

```
lib/
├── core/                   # Core functionality
│   ├── config/            # App configuration
│   ├── theme/             # App theming
│   ├── router/            # Navigation routing
│   ├── services/          # Core services
│   └── utils/             # Utility functions
├── features/              # Feature modules
│   ├── auth/              # Authentication
│   ├── dashboard/         # Dashboard screens
│   ├── patients/          # Patient management
│   ├── sessions/          # Session management
│   ├── hrm/               # HRM module
│   └── accounting/        # Accounting module
├── shared/                # Shared components
│   ├── widgets/           # Reusable widgets
│   ├── models/            # Data models
│   └── providers/         # Shared providers
└── main.dart              # App entry point
```

## 🧪 Testing

### Unit Tests
```bash
flutter test
```

### Integration Tests
```bash
flutter test integration_test/
```

### Widget Tests
```bash
flutter test test/widgets/
```

## 📱 Platform Support

### Android
- **Minimum SDK**: 21 (Android 5.0)
- **Target SDK**: 34 (Android 14)
- **Permissions**: Camera, Storage, Location, Notifications

### iOS
- **Minimum Version**: iOS 12.0
- **Target Version**: iOS 17.0
- **Permissions**: Camera, Photo Library, Notifications

## 🔧 Build & Deployment

### Android Build
```bash
# Debug APK
flutter build apk --debug

# Release APK
flutter build apk --release

# App Bundle for Play Store
flutter build appbundle --release
```

### iOS Build
```bash
# Debug build
flutter build ios --debug

# Release build
flutter build ios --release
```

## 📊 Performance

### Optimization Strategies
- **Lazy Loading** for large lists
- **Image Caching** with cached_network_image
- **State Optimization** with Riverpod
- **Bundle Size** optimization with tree shaking

### Monitoring
- **Firebase Crashlytics** for crash reporting
- **Firebase Analytics** for user behavior
- **Performance Monitoring** for app metrics

## 🔒 Security

### Data Protection
- **Encrypted Local Storage** with Hive encryption
- **Secure API Communication** with HTTPS
- **Token Management** with automatic refresh
- **Biometric Authentication** for sensitive actions

### Privacy
- **GDPR Compliance** with data consent
- **Data Minimization** principles
- **User Control** over data sharing
- **Transparent Privacy Policy**

## 🌐 Localization

### Supported Languages
- **English** (en) - Default
- **Bengali** (bn) - বাংলা

### Adding New Languages
1. Add locale to `supportedLocales` in `main.dart`
2. Create translation files in `lib/l10n/`
3. Generate localization code with `flutter gen-l10n`

## 📈 Analytics & Monitoring

### Firebase Analytics Events
- **User Authentication** events
- **Feature Usage** tracking
- **Subscription** conversions
- **Error** reporting

### Custom Metrics
- **Session Duration** tracking
- **Feature Adoption** rates
- **User Retention** analysis
- **Performance** metrics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Follow Flutter/Dart style guidelines
4. Add tests for new features
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

- **Documentation**: [Project Wiki](link-to-wiki)
- **Issues**: [GitHub Issues](link-to-issues)
- **Email**: <EMAIL>
- **Discord**: [Community Server](link-to-discord)
