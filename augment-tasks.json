{"project": "PhysioCenter SaaS App", "techStack": {"frontend_web": "React + Vite + Tailwind", "frontend_mobile": "Flutter", "backend": "Fastify (Bun.js)", "database": "MySQL", "auth": "Firebase Google Login", "notification": "Firebase Cloud Messaging", "language": "Static JSON-based i18n (English + Bengali)", "ui": {"theme": ["Dark", "Light"], "currency": "BDT (৳)"}}, "tasks": [{"title": "Initialize Backend", "description": "Set up Fastify backend with MySQL connection and environment config.", "path": "/backend", "priority": "high"}, {"title": "Firebase Auth Integration (Backend)", "description": "Add Firebase Admin SDK to verify Google Sign-In JWT and store user data with Firebase token.", "path": "/backend/modules/auth", "priority": "high"}, {"title": "Set Up Firebase FCM Token Storage", "description": "Save FCM token for each user to enable push notifications. Add DB column and update endpoints.", "path": "/backend/modules/notification", "priority": "high"}, {"title": "Initialize React Web App", "description": "Set up Vite + React with <PERSON><PERSON>wind, dark/light theme, and Firebase login.", "path": "/frontend", "priority": "high"}, {"title": "React Web: Language Switcher (EN/BN)", "description": "Use static JSON for multilingual support (i18n). Detect system language and allow toggle.", "path": "/frontend/i18n", "priority": "medium"}, {"title": "Flutter App Setup", "description": "Create Flutter app with dark/light theme, Firebase login, FCM, and EN/BN support via local JSON.", "path": "/mobile", "priority": "high"}, {"title": "Create Center Management Module", "description": "Implement backend module to create/edit centers with fields like name, address, license info, subscription status.", "path": "/backend/modules/center", "priority": "high"}, {"title": "Create Subscription Plan Logic", "description": "Define and manage subscription plans: trial, basic, pro. Add middleware to block expired users.", "path": "/backend/modules/subscription", "priority": "high"}, {"title": "Create Patient Management APIs", "description": "Allow centers to add/edit/delete patients. Include fields: name, age, phone, history, profile photo.", "path": "/backend/modules/patient", "priority": "medium"}, {"title": "Create Appointment & Therapy Session APIs", "description": "APIs to schedule sessions, assign therapists, log daily progress and pain score.", "path": "/backend/modules/session", "priority": "high"}, {"title": "Setup Notification Trigger Logic", "description": "Trigger push notifications to patients/therapists before sessions. Use FCM tokens from DB.", "path": "/backend/modules/notification", "priority": "medium"}, {"title": "Implement Web Dashboard UI", "description": "Design and implement responsive UI for dashboard (admin/center owner). Show stats, charts.", "path": "/frontend/dashboard", "priority": "medium"}, {"title": "Implement Mobile Patient View", "description": "Show upcoming appointments, therapy plan, and progress chart in Flutter.", "path": "/mobile/screens/patient_dashboard", "priority": "medium"}, {"title": "Language Files JSON Format", "description": "Define a static format like {\"home.title\": {\"en\": \"Welcome\", \"bn\": \"স্বাগতম\"}} for both platforms.", "path": "/shared/i18n", "priority": "medium"}, {"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "description": "Ensure all prices use BDT (৳) format across app and backend response.", "path": "/shared/utils/currency.js", "priority": "low"}, {"title": "Prepare Docker + Dev Deployment Script", "description": "Add Docker support for backend and local MySQL. Optional Vercel config for web.", "path": "/devops", "priority": "medium"}]}