{"name": "physio-center-frontend", "version": "1.0.0", "description": "PhysioCenter SaaS Frontend Web Application", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,css,md}\"", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "firebase": "^10.7.1", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-datepicker": "^4.24.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-select": "^5.8.0", "recharts": "^2.8.0", "tailwind-merge": "^2.1.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^3.3.6", "vite": "^5.0.8", "vitest": "^1.0.4"}, "engines": {"node": ">=18.0.0"}}