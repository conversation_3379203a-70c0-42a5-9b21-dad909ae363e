/**
 * Currency formatting utilities for BDT (Bangladeshi Taka)
 */

// Currency configuration
const CURRENCY_CONFIG = {
  BDT: {
    symbol: '৳',
    code: 'BDT',
    name: 'Bangladeshi Taka',
    nameBn: 'বাংলাদেশী টাকা',
    decimals: 2,
    thousandSeparator: ',',
    decimalSeparator: '.',
    symbolPosition: 'before', // 'before' or 'after'
    spaceAfterSymbol: false
  }
};

/**
 * Format amount as BDT currency
 * @param {number} amount - The amount to format
 * @param {object} options - Formatting options
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, options = {}) => {
  const {
    currency = 'BDT',
    showSymbol = true,
    showCode = false,
    decimals = null,
    locale = 'en-BD',
    compact = false
  } = options;

  const config = CURRENCY_CONFIG[currency];
  if (!config) {
    throw new Error(`Unsupported currency: ${currency}`);
  }

  // Handle null/undefined amounts
  if (amount === null || amount === undefined || isNaN(amount)) {
    return showSymbol ? `${config.symbol}0` : '0';
  }

  const numAmount = Number(amount);
  const decimalPlaces = decimals !== null ? decimals : config.decimals;

  // Format for compact display (K, M, B)
  if (compact) {
    return formatCompactCurrency(numAmount, config, showSymbol, showCode);
  }

  // Use Intl.NumberFormat for proper localization
  const formatter = new Intl.NumberFormat(locale, {
    style: 'decimal',
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces
  });

  const formattedNumber = formatter.format(Math.abs(numAmount));
  const isNegative = numAmount < 0;
  
  let result = '';
  
  // Add currency symbol
  if (showSymbol) {
    if (config.symbolPosition === 'before') {
      result = config.symbol;
      if (config.spaceAfterSymbol) result += ' ';
    }
  }
  
  // Add negative sign
  if (isNegative) {
    result += '-';
  }
  
  // Add formatted number
  result += formattedNumber;
  
  // Add currency symbol after
  if (showSymbol && config.symbolPosition === 'after') {
    if (config.spaceAfterSymbol) result = ' ' + result;
    result += config.symbol;
  }
  
  // Add currency code
  if (showCode) {
    result += ` ${config.code}`;
  }
  
  return result;
};

/**
 * Format currency in compact form (K, M, B)
 * @param {number} amount - The amount to format
 * @param {object} config - Currency configuration
 * @param {boolean} showSymbol - Whether to show currency symbol
 * @param {boolean} showCode - Whether to show currency code
 * @returns {string} Compact formatted currency
 */
const formatCompactCurrency = (amount, config, showSymbol, showCode) => {
  const absAmount = Math.abs(amount);
  const isNegative = amount < 0;
  
  let value = absAmount;
  let suffix = '';
  
  if (absAmount >= 1e9) {
    value = absAmount / 1e9;
    suffix = 'B';
  } else if (absAmount >= 1e6) {
    value = absAmount / 1e6;
    suffix = 'M';
  } else if (absAmount >= 1e3) {
    value = absAmount / 1e3;
    suffix = 'K';
  }
  
  // Format the value with appropriate decimals
  const decimals = value >= 100 ? 0 : value >= 10 ? 1 : 2;
  const formattedValue = value.toFixed(decimals);
  
  let result = '';
  
  if (showSymbol) {
    result = config.symbol;
    if (config.spaceAfterSymbol) result += ' ';
  }
  
  if (isNegative) result += '-';
  result += formattedValue + suffix;
  
  if (showCode) {
    result += ` ${config.code}`;
  }
  
  return result;
};

/**
 * Parse currency string to number
 * @param {string} currencyString - Currency string to parse
 * @param {string} currency - Currency code
 * @returns {number} Parsed amount
 */
export const parseCurrency = (currencyString, currency = 'BDT') => {
  if (!currencyString || typeof currencyString !== 'string') {
    return 0;
  }
  
  const config = CURRENCY_CONFIG[currency];
  if (!config) {
    throw new Error(`Unsupported currency: ${currency}`);
  }
  
  // Remove currency symbol, code, and spaces
  let cleanString = currencyString
    .replace(config.symbol, '')
    .replace(config.code, '')
    .replace(/\s/g, '');
  
  // Handle negative numbers
  const isNegative = cleanString.includes('-');
  cleanString = cleanString.replace('-', '');
  
  // Remove thousand separators and convert decimal separator
  cleanString = cleanString
    .replace(new RegExp(`\\${config.thousandSeparator}`, 'g'), '')
    .replace(config.decimalSeparator, '.');
  
  const amount = parseFloat(cleanString) || 0;
  return isNegative ? -amount : amount;
};

/**
 * Format amount in words (Bengali and English)
 * @param {number} amount - Amount to convert to words
 * @param {string} language - Language ('en' or 'bn')
 * @returns {string} Amount in words
 */
export const formatCurrencyInWords = (amount, language = 'en') => {
  if (language === 'bn') {
    return formatBengaliCurrencyInWords(amount);
  }
  return formatEnglishCurrencyInWords(amount);
};

/**
 * Format amount in English words
 * @param {number} amount - Amount to format
 * @returns {string} Amount in English words
 */
const formatEnglishCurrencyInWords = (amount) => {
  const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
  const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
  const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
  const thousands = ['', 'Thousand', 'Lakh', 'Crore'];
  
  if (amount === 0) return 'Zero Taka';
  
  const isNegative = amount < 0;
  amount = Math.abs(amount);
  
  const [integerPart, decimalPart] = amount.toFixed(2).split('.');
  const integer = parseInt(integerPart);
  const decimal = parseInt(decimalPart);
  
  let result = '';
  
  if (isNegative) result += 'Minus ';
  
  if (integer > 0) {
    result += convertIntegerToWords(integer, ones, teens, tens, thousands) + ' Taka';
  }
  
  if (decimal > 0) {
    if (integer > 0) result += ' and ';
    result += convertIntegerToWords(decimal, ones, teens, tens, []) + ' Paisa';
  }
  
  return result || 'Zero Taka';
};

/**
 * Format amount in Bengali words
 * @param {number} amount - Amount to format
 * @returns {string} Amount in Bengali words
 */
const formatBengaliCurrencyInWords = (amount) => {
  const ones = ['', 'এক', 'দুই', 'তিন', 'চার', 'পাঁচ', 'ছয়', 'সাত', 'আট', 'নয়'];
  const teens = ['দশ', 'এগার', 'বার', 'তের', 'চৌদ্দ', 'পনের', 'ষোল', 'সতের', 'আঠার', 'উনিশ'];
  const tens = ['', '', 'বিশ', 'ত্রিশ', 'চল্লিশ', 'পঞ্চাশ', 'ষাট', 'সত্তর', 'আশি', 'নব্বই'];
  const thousands = ['', 'হাজার', 'লক্ষ', 'কোটি'];
  
  if (amount === 0) return 'শূন্য টাকা';
  
  const isNegative = amount < 0;
  amount = Math.abs(amount);
  
  const [integerPart, decimalPart] = amount.toFixed(2).split('.');
  const integer = parseInt(integerPart);
  const decimal = parseInt(decimalPart);
  
  let result = '';
  
  if (isNegative) result += 'মাইনাস ';
  
  if (integer > 0) {
    result += convertIntegerToWords(integer, ones, teens, tens, thousands) + ' টাকা';
  }
  
  if (decimal > 0) {
    if (integer > 0) result += ' ';
    result += convertIntegerToWords(decimal, ones, teens, tens, []) + ' পয়সা';
  }
  
  return result || 'শূন্য টাকা';
};

/**
 * Convert integer to words helper function
 * @param {number} num - Number to convert
 * @param {array} ones - Ones array
 * @param {array} teens - Teens array
 * @param {array} tens - Tens array
 * @param {array} thousands - Thousands array
 * @returns {string} Number in words
 */
const convertIntegerToWords = (num, ones, teens, tens, thousands) => {
  if (num === 0) return '';
  
  let result = '';
  let thousandIndex = 0;
  
  while (num > 0) {
    const chunk = num % 1000;
    if (chunk > 0) {
      let chunkWords = '';
      
      const hundreds = Math.floor(chunk / 100);
      const remainder = chunk % 100;
      
      if (hundreds > 0) {
        chunkWords += ones[hundreds] + ' Hundred ';
      }
      
      if (remainder >= 10 && remainder < 20) {
        chunkWords += teens[remainder - 10];
      } else {
        const tensDigit = Math.floor(remainder / 10);
        const onesDigit = remainder % 10;
        
        if (tensDigit > 0) {
          chunkWords += tens[tensDigit] + ' ';
        }
        
        if (onesDigit > 0) {
          chunkWords += ones[onesDigit];
        }
      }
      
      if (thousands[thousandIndex]) {
        chunkWords += ' ' + thousands[thousandIndex];
      }
      
      result = chunkWords.trim() + ' ' + result;
    }
    
    num = Math.floor(num / 1000);
    thousandIndex++;
  }
  
  return result.trim();
};

/**
 * Validate currency amount
 * @param {string|number} amount - Amount to validate
 * @param {object} options - Validation options
 * @returns {object} Validation result
 */
export const validateCurrencyAmount = (amount, options = {}) => {
  const {
    min = 0,
    max = Number.MAX_SAFE_INTEGER,
    allowNegative = false,
    allowZero = true
  } = options;
  
  const numAmount = typeof amount === 'string' ? parseCurrency(amount) : Number(amount);
  
  if (isNaN(numAmount)) {
    return { isValid: false, error: 'Invalid amount format' };
  }
  
  if (!allowNegative && numAmount < 0) {
    return { isValid: false, error: 'Negative amounts not allowed' };
  }
  
  if (!allowZero && numAmount === 0) {
    return { isValid: false, error: 'Zero amount not allowed' };
  }
  
  if (numAmount < min) {
    return { isValid: false, error: `Amount must be at least ${formatCurrency(min)}` };
  }
  
  if (numAmount > max) {
    return { isValid: false, error: `Amount must not exceed ${formatCurrency(max)}` };
  }
  
  return { isValid: true, amount: numAmount };
};

/**
 * Get currency configuration
 * @param {string} currency - Currency code
 * @returns {object} Currency configuration
 */
export const getCurrencyConfig = (currency = 'BDT') => {
  return CURRENCY_CONFIG[currency] || CURRENCY_CONFIG.BDT;
};

/**
 * Format percentage with BDT context
 * @param {number} value - Percentage value
 * @param {number} decimals - Decimal places
 * @returns {string} Formatted percentage
 */
export const formatPercentage = (value, decimals = 1) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '0%';
  }
  
  return `${Number(value).toFixed(decimals)}%`;
};

// Export currency configuration for external use
export { CURRENCY_CONFIG };
