import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Building2, Plus } from 'lucide-react';

const CentersPage = () => {
  const { t } = useLanguage();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('centers.title')}</h1>
          <p className="text-muted-foreground">Manage physiotherapy centers (Admin only)</p>
        </div>
        <button className="btn btn-primary">
          <Plus className="w-4 h-4" />
          <span className="ml-2">{t('centers.addCenter')}</span>
        </button>
      </div>

      <div className="card">
        <div className="card-content p-6">
          <div className="text-center py-12">
            <Building2 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Centers Management</h3>
            <p className="text-muted-foreground mb-4">
              View and manage all physiotherapy centers in the system.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CentersPage;
