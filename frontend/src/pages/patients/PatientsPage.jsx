import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Users, Plus, Search, Filter } from 'lucide-react';

const PatientsPage = () => {
  const { t } = useLanguage();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('patients.title')}</h1>
          <p className="text-muted-foreground">Manage your patients and their records</p>
        </div>
        <button className="btn btn-primary">
          <Plus className="w-4 h-4" />
          <span className="ml-2">{t('patients.addPatient')}</span>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="card-content p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder={t('patients.searchPatients')}
                  className="input pl-10 w-full"
                />
              </div>
            </div>
            <button className="btn btn-outline">
              <Filter className="w-4 h-4" />
              <span className="ml-2">{t('common.filter')}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Patients List */}
      <div className="card">
        <div className="card-content p-6">
          <div className="text-center py-12">
            <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No patients yet</h3>
            <p className="text-muted-foreground mb-4">
              Start by adding your first patient to the system.
            </p>
            <button className="btn btn-primary">
              <Plus className="w-4 h-4" />
              <span className="ml-2">{t('patients.addPatient')}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientsPage;
