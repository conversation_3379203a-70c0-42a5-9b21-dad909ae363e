import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { LogIn, Chrome, Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';

const LoginPage = () => {
  const [loading, setLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState('CENTER_OWNER');
  const { signInWithGoogle } = useAuth();
  const { t } = useLanguage();

  const handleGoogleSignIn = async () => {
    try {
      setLoading(true);
      await signInWithGoogle(selectedRole);
    } catch (error) {
      console.error('Sign in error:', error);
      // Error is already handled in AuthContext with toast
    } finally {
      setLoading(false);
    }
  };

  const roles = [
    {
      value: 'CENTER_OWNER',
      label: 'Center Owner',
      description: 'Manage your physiotherapy center, staff, and patients'
    },
    {
      value: 'THERAPIST',
      label: 'Therapist',
      description: 'Access patient records and manage therapy sessions'
    },
    {
      value: 'PATIENT',
      label: 'Patient',
      description: 'View your appointments and track your progress'
    }
  ];

  return (
    <div className="card animate-fade-in">
      <div className="card-header text-center">
        <h1 className="card-title text-2xl">{t('auth.welcomeToPhysioCenter')}</h1>
        <p className="card-description">{t('auth.signInToContinue')}</p>
      </div>

      <div className="card-content space-y-6">
        {/* Role Selection */}
        <div className="space-y-3">
          <label className="form-label">Select your role</label>
          <div className="space-y-2">
            {roles.map((role) => (
              <label
                key={role.value}
                className={`flex items-start gap-3 p-3 border rounded-lg cursor-pointer transition-colors hover:bg-accent ${
                  selectedRole === role.value
                    ? 'border-primary bg-primary/5'
                    : 'border-border'
                }`}
              >
                <input
                  type="radio"
                  name="role"
                  value={role.value}
                  checked={selectedRole === role.value}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  className="mt-1"
                />
                <div className="flex-1">
                  <div className="font-medium text-sm">{role.label}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {role.description}
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Sign In Button */}
        <button
          onClick={handleGoogleSignIn}
          disabled={loading}
          className="btn btn-primary w-full"
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Chrome className="w-4 h-4" />
          )}
          <span className="ml-2">
            {loading ? t('common.loading') : t('auth.signInWithGoogle')}
          </span>
        </button>

        {/* Features Preview */}
        <div className="pt-4 border-t">
          <h3 className="font-medium text-sm mb-3">What you get with PhysioCenter:</h3>
          <div className="space-y-2 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
              <span>Patient management and records</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
              <span>Appointment scheduling</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
              <span>Progress tracking and reports</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
              <span>Multi-branch support (Pro plan)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
              <span>HRM and accounting modules</span>
            </div>
          </div>
        </div>

        {/* Trial Info */}
        <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-primary-foreground text-xs font-bold">!</span>
            </div>
            <div className="text-sm">
              <div className="font-medium text-primary mb-1">Free 30-day trial</div>
              <div className="text-muted-foreground">
                Start with a free trial. No credit card required. Upgrade anytime to unlock advanced features.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
