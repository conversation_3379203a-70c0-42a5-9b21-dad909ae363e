import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Bell, CheckCheck } from 'lucide-react';

const NotificationsPage = () => {
  const { t } = useLanguage();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('notifications.title')}</h1>
          <p className="text-muted-foreground">Stay updated with important notifications</p>
        </div>
        <button className="btn btn-outline">
          <CheckCheck className="w-4 h-4" />
          <span className="ml-2">{t('notifications.markAllAsRead')}</span>
        </button>
      </div>

      <div className="card">
        <div className="card-content p-6">
          <div className="text-center py-12">
            <Bell className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No notifications</h3>
            <p className="text-muted-foreground">
              You're all caught up! New notifications will appear here.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationsPage;
