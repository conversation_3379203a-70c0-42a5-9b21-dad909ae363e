import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { UserCheck, Plus } from 'lucide-react';

const TherapistsPage = () => {
  const { t } = useLanguage();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('therapists.title')}</h1>
          <p className="text-muted-foreground">Manage your therapy staff</p>
        </div>
        <button className="btn btn-primary">
          <Plus className="w-4 h-4" />
          <span className="ml-2">{t('therapists.addTherapist')}</span>
        </button>
      </div>

      <div className="card">
        <div className="card-content p-6">
          <div className="text-center py-12">
            <UserCheck className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No therapists added</h3>
            <p className="text-muted-foreground mb-4">
              Add therapists to your center to start managing sessions.
            </p>
            <button className="btn btn-primary">
              <Plus className="w-4 h-4" />
              <span className="ml-2">{t('therapists.addTherapist')}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TherapistsPage;
