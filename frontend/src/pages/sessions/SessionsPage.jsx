import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Calendar, Plus } from 'lucide-react';

const SessionsPage = () => {
  const { t } = useLanguage();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('sessions.title')}</h1>
          <p className="text-muted-foreground">Manage therapy sessions and appointments</p>
        </div>
        <button className="btn btn-primary">
          <Plus className="w-4 h-4" />
          <span className="ml-2">{t('sessions.addSession')}</span>
        </button>
      </div>

      <div className="card">
        <div className="card-content p-6">
          <div className="text-center py-12">
            <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No sessions scheduled</h3>
            <p className="text-muted-foreground mb-4">
              Schedule your first therapy session.
            </p>
            <button className="btn btn-primary">
              <Plus className="w-4 h-4" />
              <span className="ml-2">{t('sessions.addSession')}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionsPage;
