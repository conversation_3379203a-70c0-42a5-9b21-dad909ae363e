import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Settings, User, Bell, Shield, CreditCard } from 'lucide-react';

const SettingsPage = () => {
  const { t } = useLanguage();

  const settingsSections = [
    {
      title: t('settings.general'),
      description: 'Basic application settings',
      icon: Settings,
      items: [
        { name: t('settings.language'), value: 'English' },
        { name: t('settings.theme'), value: 'System' }
      ]
    },
    {
      title: t('settings.account'),
      description: 'Manage your account information',
      icon: User,
      items: [
        { name: 'Display Name', value: '<PERSON>' },
        { name: 'Email', value: '<EMAIL>' }
      ]
    },
    {
      title: t('settings.notifications'),
      description: 'Configure notification preferences',
      icon: Bell,
      items: [
        { name: t('settings.emailNotifications'), value: 'Enabled' },
        { name: t('settings.pushNotifications'), value: 'Enabled' }
      ]
    },
    {
      title: t('settings.security'),
      description: 'Security and privacy settings',
      icon: Shield,
      items: [
        { name: 'Two-Factor Authentication', value: 'Disabled' },
        { name: 'Login Sessions', value: '2 active' }
      ]
    },
    {
      title: t('settings.billing'),
      description: 'Subscription and billing information',
      icon: CreditCard,
      items: [
        { name: 'Current Plan', value: 'Trial' },
        { name: 'Next Billing', value: 'N/A' }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">{t('settings.title')}</h1>
        <p className="text-muted-foreground">Manage your application preferences and account settings</p>
      </div>

      <div className="grid gap-6">
        {settingsSections.map((section) => (
          <div key={section.title} className="card">
            <div className="card-header">
              <div className="flex items-center gap-3">
                <section.icon className="w-5 h-5 text-primary" />
                <div>
                  <h3 className="card-title">{section.title}</h3>
                  <p className="text-sm text-muted-foreground">{section.description}</p>
                </div>
              </div>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                {section.items.map((item) => (
                  <div key={item.name} className="flex items-center justify-between py-2">
                    <span className="text-sm font-medium">{item.name}</span>
                    <span className="text-sm text-muted-foreground">{item.value}</span>
                  </div>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t">
                <button className="btn btn-outline">
                  Configure
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SettingsPage;
