import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { User, Mail, Shield, Calendar, Building2 } from 'lucide-react';

const ProfilePage = () => {
  const { user } = useAuth();
  const { t, formatDate } = useLanguage();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">{t('navigation.profile')}</h1>
        <p className="text-muted-foreground">View and manage your profile information</p>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Profile Card */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-content p-6">
              <div className="text-center">
                <div className="w-24 h-24 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                  {user?.photoURL ? (
                    <img
                      src={user.photoURL}
                      alt={user.displayName}
                      className="w-24 h-24 rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-12 h-12 text-primary-foreground" />
                  )}
                </div>
                <h3 className="text-xl font-semibold">{user?.displayName}</h3>
                <p className="text-muted-foreground">{user?.role}</p>
                <div className="mt-4">
                  <span className={`badge ${user?.isActive ? 'badge-default' : 'badge-destructive'}`}>
                    {user?.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Profile Details */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Profile Information</h3>
            </div>
            <div className="card-content">
              <div className="space-y-6">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Email</p>
                      <p className="text-sm text-muted-foreground">{user?.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Shield className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Role</p>
                      <p className="text-sm text-muted-foreground">{user?.role}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Calendar className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Member Since</p>
                      <p className="text-sm text-muted-foreground">
                        {user?.createdAt ? formatDate(user.createdAt) : 'N/A'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <User className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Firebase UID</p>
                      <p className="text-sm text-muted-foreground font-mono text-xs">
                        {user?.firebaseUid}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Associated Centers */}
                {user?.ownedCenters?.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3 flex items-center gap-2">
                      <Building2 className="w-4 h-4" />
                      Owned Centers
                    </h4>
                    <div className="space-y-2">
                      {user.ownedCenters.map((center) => (
                        <div key={center.id} className="p-3 bg-accent/50 rounded-lg">
                          <p className="font-medium">{center.name}</p>
                          <p className="text-sm text-muted-foreground">
                            Plan: {center.subscriptionPlan} • 
                            Status: {center.isActive ? 'Active' : 'Inactive'}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Therapist Profile */}
                {user?.therapistProfile && (
                  <div>
                    <h4 className="font-medium mb-3">Therapist Information</h4>
                    <div className="p-3 bg-accent/50 rounded-lg">
                      <p className="text-sm">
                        <span className="font-medium">Specialization:</span> {user.therapistProfile.specialization}
                      </p>
                      <p className="text-sm">
                        <span className="font-medium">Experience:</span> {user.therapistProfile.experience} years
                      </p>
                      <p className="text-sm">
                        <span className="font-medium">License:</span> {user.therapistProfile.licenseNumber}
                      </p>
                    </div>
                  </div>
                )}

                <div className="pt-4 border-t">
                  <button className="btn btn-primary">
                    Edit Profile
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
