import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  Users, 
  Calendar, 
  DollarSign, 
  TrendingUp,
  Clock,
  UserCheck,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

const DashboardPage = () => {
  const { user, hasRole } = useAuth();
  const { t, formatCurrency } = useLanguage();

  // Mock data - in real app, this would come from API
  const stats = {
    totalPatients: 156,
    todaysSessions: 12,
    monthlyRevenue: 45000,
    activeTherapists: 8,
    pendingAppointments: 5,
    completedSessions: 89
  };

  const recentActivities = [
    {
      id: 1,
      type: 'session',
      message: 'Session completed with <PERSON>',
      time: '2 hours ago',
      icon: CheckCircle,
      color: 'text-green-600'
    },
    {
      id: 2,
      type: 'appointment',
      message: 'New appointment scheduled for tomorrow',
      time: '3 hours ago',
      icon: Calendar,
      color: 'text-blue-600'
    },
    {
      id: 3,
      type: 'patient',
      message: 'New patient registered: <PERSON>',
      time: '5 hours ago',
      icon: Users,
      color: 'text-purple-600'
    },
    {
      id: 4,
      type: 'alert',
      message: 'Payment reminder sent to 3 patients',
      time: '1 day ago',
      icon: AlertCircle,
      color: 'text-orange-600'
    }
  ];

  const upcomingSessions = [
    {
      id: 1,
      patient: 'John Doe',
      therapist: 'Dr. <PERSON> <PERSON>',
      time: '10:00 AM',
      type: 'Therapy Session'
    },
    {
      id: 2,
      patient: 'Jane Smith',
      therapist: 'Dr. Mike <PERSON>',
      time: '11:30 AM',
      type: 'Consultation'
    },
    {
      id: 3,
      patient: 'Bob Brown',
      therapist: 'Dr. Sarah Wilson',
      time: '2:00 PM',
      type: 'Follow-up'
    }
  ];

  const StatCard = ({ title, value, icon: Icon, trend, color = 'text-primary' }) => (
    <div className="card">
      <div className="card-content p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {trend && (
              <p className="text-xs text-muted-foreground mt-1">
                <TrendingUp className="w-3 h-3 inline mr-1" />
                {trend}
              </p>
            )}
          </div>
          <Icon className={`w-8 h-8 ${color}`} />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('dashboard.title')}</h1>
        <p className="text-muted-foreground">
          {t('dashboard.welcome', { name: user?.displayName || 'User' })}
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title={t('dashboard.totalPatients')}
          value={stats.totalPatients}
          icon={Users}
          trend="+12% from last month"
          color="text-blue-600"
        />
        <StatCard
          title={t('dashboard.todaysSessions')}
          value={stats.todaysSessions}
          icon={Calendar}
          trend="+5 from yesterday"
          color="text-green-600"
        />
        <StatCard
          title={t('dashboard.monthlyRevenue')}
          value={formatCurrency(stats.monthlyRevenue)}
          icon={DollarSign}
          trend="+8% from last month"
          color="text-purple-600"
        />
        <StatCard
          title={t('dashboard.activeTherapists')}
          value={stats.activeTherapists}
          icon={UserCheck}
          color="text-orange-600"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Sessions */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">{t('dashboard.todaysSessions')}</h3>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {upcomingSessions.map((session) => (
                <div key={session.id} className="flex items-center justify-between p-3 bg-accent/50 rounded-lg">
                  <div>
                    <p className="font-medium">{session.patient}</p>
                    <p className="text-sm text-muted-foreground">{session.therapist}</p>
                    <p className="text-xs text-muted-foreground">{session.type}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{session.time}</p>
                    <Clock className="w-4 h-4 text-muted-foreground ml-auto mt-1" />
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <button className="btn btn-outline w-full">
                {t('dashboard.viewAll')}
              </button>
            </div>
          </div>
        </div>

        {/* Recent Activities */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Recent Activities</h3>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3">
                  <activity.icon className={`w-5 h-5 mt-0.5 ${activity.color}`} />
                  <div className="flex-1">
                    <p className="text-sm">{activity.message}</p>
                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      {hasRole('CENTER_OWNER') && (
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Quick Actions</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <button className="btn btn-outline flex flex-col items-center gap-2 h-auto py-4">
                <Users className="w-6 h-6" />
                <span className="text-sm">Add Patient</span>
              </button>
              <button className="btn btn-outline flex flex-col items-center gap-2 h-auto py-4">
                <Calendar className="w-6 h-6" />
                <span className="text-sm">Schedule Session</span>
              </button>
              <button className="btn btn-outline flex flex-col items-center gap-2 h-auto py-4">
                <UserCheck className="w-6 h-6" />
                <span className="text-sm">Add Therapist</span>
              </button>
              <button className="btn btn-outline flex flex-col items-center gap-2 h-auto py-4">
                <TrendingUp className="w-6 h-6" />
                <span className="text-sm">View Reports</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardPage;
