import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import {
  LayoutDashboard,
  Users,
  Calendar,
  UserCheck,
  Building2,
  Bell,
  Settings,
  User,
  LogOut,
  Menu,
  X,
  Sun,
  Moon,
  Globe,
  UserPlus,
  Receipt,
  DollarSign,
  FileText
} from 'lucide-react';
import LoadingSpinner from '../ui/LoadingSpinner';

const DashboardLayout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, signOut, hasRole, hasFeature } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const { t, language, changeLanguage, supportedLanguages } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  // Navigation items based on user role and subscription
  const getNavigationItems = () => {
    const items = [
      {
        name: t('navigation.dashboard'),
        href: '/dashboard',
        icon: LayoutDashboard,
        roles: ['CENTER_OWNER', 'THERAPIST', 'PATIENT', 'SUPER_ADMIN']
      },
      {
        name: t('navigation.patients'),
        href: '/patients',
        icon: Users,
        roles: ['CENTER_OWNER', 'THERAPIST', 'SUPER_ADMIN']
      },
      {
        name: t('navigation.sessions'),
        href: '/sessions',
        icon: Calendar,
        roles: ['CENTER_OWNER', 'THERAPIST', 'PATIENT', 'SUPER_ADMIN']
      },
      {
        name: t('navigation.therapists'),
        href: '/therapists',
        icon: UserCheck,
        roles: ['CENTER_OWNER', 'SUPER_ADMIN']
      },
      {
        name: t('navigation.centers'),
        href: '/centers',
        icon: Building2,
        roles: ['SUPER_ADMIN']
      }
    ];

    // Add HRM section if user has access
    if (hasFeature && hasFeature('hrmModule')) {
      items.push({
        name: 'Employees',
        href: '/employees',
        icon: UserPlus,
        roles: ['CENTER_OWNER', 'SUPER_ADMIN'],
        feature: 'hrmModule'
      });
    }

    // Add Accounting section if user has access
    if (hasFeature && hasFeature('accountingModule')) {
      items.push(
        {
          name: 'Expenses',
          href: '/expenses',
          icon: Receipt,
          roles: ['CENTER_OWNER', 'SUPER_ADMIN'],
          feature: 'accountingModule'
        },
        {
          name: 'Invoices',
          href: '/invoices',
          icon: FileText,
          roles: ['CENTER_OWNER', 'SUPER_ADMIN'],
          feature: 'accountingModule'
        }
      );
    }

    // Add common items
    items.push(
      {
        name: t('navigation.notifications'),
        href: '/notifications',
        icon: Bell,
        roles: ['CENTER_OWNER', 'THERAPIST', 'PATIENT', 'SUPER_ADMIN']
      },
      {
        name: t('navigation.settings'),
        href: '/settings',
        icon: Settings,
        roles: ['CENTER_OWNER', 'THERAPIST', 'PATIENT', 'SUPER_ADMIN']
      }
    );

    return items.filter(item => 
      item.roles.includes(user?.role) && 
      (!item.feature || (hasFeature && hasFeature(item.feature)))
    );
  };

  const navigationItems = getNavigationItems();

  if (!user) {
    return <LoadingSpinner fullScreen text={t('common.loading')} />;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-card border-r transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center gap-2 p-6 border-b">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">PC</span>
            </div>
            <span className="font-semibold text-lg">PhysioCenter</span>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {navigationItems.map((item) => {
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.href}
                  to={item.href}
                  className={`nav-link ${isActive ? 'active' : ''}`}
                  onClick={() => setSidebarOpen(false)}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* User menu */}
          <div className="p-4 border-t">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                {user.photoURL ? (
                  <img
                    src={user.photoURL}
                    alt={user.displayName}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <User className="w-5 h-5 text-primary-foreground" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{user.displayName}</p>
                <p className="text-xs text-muted-foreground truncate">{user.role}</p>
              </div>
            </div>

            <div className="space-y-2">
              <Link
                to="/profile"
                className="flex items-center gap-2 w-full text-left text-sm p-2 rounded-md hover:bg-accent transition-colors"
                onClick={() => setSidebarOpen(false)}
              >
                <User className="w-4 h-4" />
                {t('navigation.profile')}
              </Link>
              <button
                onClick={handleSignOut}
                className="flex items-center gap-2 w-full text-left text-sm p-2 rounded-md hover:bg-accent transition-colors text-destructive"
              >
                <LogOut className="w-4 h-4" />
                {t('common.logout')}
              </button>
            </div>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Top bar */}
        <header className="bg-card border-b px-4 py-3 lg:px-6">
          <div className="flex items-center justify-between">
            <button
              onClick={() => setSidebarOpen(true)}
              className="btn btn-ghost btn-sm lg:hidden"
            >
              <Menu className="w-5 h-5" />
            </button>

            <div className="flex items-center gap-2">
              {/* Language Switcher */}
              <div className="relative group">
                <button className="btn btn-ghost btn-sm">
                  <Globe className="w-4 h-4" />
                  <span className="ml-1 text-xs uppercase hidden sm:inline">{language}</span>
                </button>
                <div className="absolute right-0 top-full mt-1 bg-card border rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-20">
                  {supportedLanguages.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => changeLanguage(lang.code)}
                      className={`block w-full text-left px-3 py-2 text-sm hover:bg-accent transition-colors ${
                        language === lang.code ? 'bg-accent text-accent-foreground' : ''
                      }`}
                    >
                      {lang.nativeName}
                    </button>
                  ))}
                </div>
              </div>

              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className="btn btn-ghost btn-sm"
                aria-label="Toggle theme"
              >
                {theme === 'dark' ? (
                  <Sun className="w-4 h-4" />
                ) : (
                  <Moon className="w-4 h-4" />
                )}
              </button>

              {/* Notifications */}
              <Link to="/notifications" className="btn btn-ghost btn-sm">
                <Bell className="w-4 h-4" />
              </Link>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-4 lg:p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
