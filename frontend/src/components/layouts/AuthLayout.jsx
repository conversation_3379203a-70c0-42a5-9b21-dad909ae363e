import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Sun, Moon, Globe } from 'lucide-react';

const AuthLayout = ({ children }) => {
  const { theme, toggleTheme } = useTheme();
  const { language, changeLanguage, supportedLanguages } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5">
      {/* Header */}
      <header className="absolute top-0 left-0 right-0 z-10 p-4">
        <div className="flex justify-between items-center max-w-7xl mx-auto">
          {/* Logo */}
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">PC</span>
            </div>
            <span className="font-semibold text-lg text-foreground">PhysioCenter</span>
          </div>

          {/* Controls */}
          <div className="flex items-center gap-2">
            {/* Language Switcher */}
            <div className="relative group">
              <button className="btn btn-ghost btn-sm">
                <Globe className="w-4 h-4" />
                <span className="ml-1 text-xs uppercase">{language}</span>
              </button>
              <div className="absolute right-0 top-full mt-1 bg-card border rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-20">
                {supportedLanguages.map((lang) => (
                  <button
                    key={lang.code}
                    onClick={() => changeLanguage(lang.code)}
                    className={`block w-full text-left px-3 py-2 text-sm hover:bg-accent transition-colors ${
                      language === lang.code ? 'bg-accent text-accent-foreground' : ''
                    }`}
                  >
                    {lang.nativeName}
                  </button>
                ))}
              </div>
            </div>

            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className="btn btn-ghost btn-sm"
              aria-label="Toggle theme"
            >
              {theme === 'dark' ? (
                <Sun className="w-4 h-4" />
              ) : (
                <Moon className="w-4 h-4" />
              )}
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="absolute bottom-0 left-0 right-0 p-4">
        <div className="text-center text-sm text-muted-foreground">
          <p>&copy; 2024 PhysioCenter. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default AuthLayout;
