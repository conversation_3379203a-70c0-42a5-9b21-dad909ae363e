import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Calendar, 
  Search, 
  Plus, 
  Edit, 
  Eye, 
  Trash2, 
  Clock, 
  User, 
  Filter,
  Download,
  CalendarPlus,
  CheckCircle,
  AlertCircle,
  PlayCircle
} from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import toast from 'react-hot-toast';

const SessionList = () => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDate, setFilterDate] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sessionsPerPage] = useState(10);
  const navigate = useNavigate();
  const { t } = useLanguage();

  useEffect(() => {
    fetchSessions();
  }, []);

  const fetchSessions = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('centerOwnerToken');
      
      if (!token) {
        navigate('/center/login');
        return;
      }

      const response = await fetch('http://localhost:3000/api/center/sessions', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch sessions');
      }

      const data = await response.json();
      if (data.success) {
        setSessions(data.data);
      } else {
        throw new Error(data.message || 'Failed to fetch sessions');
      }
    } catch (error) {
      console.error('Fetch sessions error:', error);
      toast.error('Failed to load sessions');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteSession = async (sessionId) => {
    if (!window.confirm('Are you sure you want to delete this session?')) {
      return;
    }

    try {
      const token = localStorage.getItem('centerOwnerToken');
      const response = await fetch(`http://localhost:3000/api/center/sessions/${sessionId}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${token}` }
      });

      if (!response.ok) {
        throw new Error('Failed to delete session');
      }

      const data = await response.json();
      if (data.success) {
        setSessions(sessions.filter(s => s.id !== sessionId));
        toast.success('Session deleted successfully');
      } else {
        throw new Error(data.message || 'Failed to delete session');
      }
    } catch (error) {
      console.error('Delete session error:', error);
      toast.error('Failed to delete session');
    }
  };

  const handleUpdateStatus = async (sessionId, newStatus) => {
    try {
      const token = localStorage.getItem('centerOwnerToken');
      const response = await fetch(`http://localhost:3000/api/center/sessions/${sessionId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        throw new Error('Failed to update session status');
      }

      const data = await response.json();
      if (data.success) {
        setSessions(sessions.map(s => 
          s.id === sessionId ? { ...s, status: newStatus } : s
        ));
        toast.success('Session status updated successfully');
      } else {
        throw new Error(data.message || 'Failed to update session status');
      }
    } catch (error) {
      console.error('Update session status error:', error);
      toast.error('Failed to update session status');
    }
  };

  // Filter and search sessions
  const filteredSessions = sessions.filter(session => {
    const matchesSearch = session.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.therapistName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.sessionType.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || session.status === filterStatus;
    
    const matchesDate = !filterDate || session.date === filterDate;
    
    return matchesSearch && matchesStatus && matchesDate;
  });

  // Pagination
  const indexOfLastSession = currentPage * sessionsPerPage;
  const indexOfFirstSession = indexOfLastSession - sessionsPerPage;
  const currentSessions = filteredSessions.slice(indexOfFirstSession, indexOfLastSession);
  const totalPages = Math.ceil(filteredSessions.length / sessionsPerPage);

  const getStatusBadge = (status) => {
    const statusConfig = {
      scheduled: { bg: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200', text: 'Scheduled', icon: Clock },
      'in-progress': { bg: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200', text: 'In Progress', icon: PlayCircle },
      completed: { bg: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200', text: 'Completed', icon: CheckCircle },
      cancelled: { bg: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200', text: 'Cancelled', icon: AlertCircle }
    };
    
    const config = statusConfig[status] || statusConfig.scheduled;
    const IconComponent = config.icon;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg}`}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.text}
      </span>
    );
  };

  const getStatusActions = (session) => {
    const actions = [];
    
    if (session.status === 'scheduled') {
      actions.push(
        <button
          key="start"
          onClick={() => handleUpdateStatus(session.id, 'in-progress')}
          className="text-blue-600 hover:text-blue-700 text-xs px-2 py-1 rounded transition-colors"
          title="Start Session"
        >
          Start
        </button>
      );
    }
    
    if (session.status === 'in-progress') {
      actions.push(
        <button
          key="complete"
          onClick={() => handleUpdateStatus(session.id, 'completed')}
          className="text-green-600 hover:text-green-700 text-xs px-2 py-1 rounded transition-colors"
          title="Complete Session"
        >
          Complete
        </button>
      );
    }
    
    if (session.status === 'scheduled' || session.status === 'in-progress') {
      actions.push(
        <button
          key="cancel"
          onClick={() => handleUpdateStatus(session.id, 'cancelled')}
          className="text-red-600 hover:text-red-700 text-xs px-2 py-1 rounded transition-colors"
          title="Cancel Session"
        >
          Cancel
        </button>
      );
    }
    
    return actions;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading sessions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <Calendar className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {t('sessions.title', 'Session Management')}
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {filteredSessions.length} {t('sessions.total', 'total sessions')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/center/sessions/new')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <CalendarPlus className="h-4 w-4" />
                <span>{t('sessions.addNew', 'New Session')}</span>
              </button>
              
              <button
                onClick={() => navigate('/center/dashboard')}
                className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 px-4 py-2 rounded-lg transition-colors"
              >
                {t('common.backToDashboard', 'Back to Dashboard')}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filter Bar */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder={t('sessions.searchPlaceholder', 'Search sessions by patient, therapist, or type...')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-5 w-5 text-gray-400" />
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">{t('common.all', 'All Status')}</option>
                  <option value="scheduled">{t('sessions.scheduled', 'Scheduled')}</option>
                  <option value="in-progress">{t('sessions.inProgress', 'In Progress')}</option>
                  <option value="completed">{t('sessions.completed', 'Completed')}</option>
                  <option value="cancelled">{t('sessions.cancelled', 'Cancelled')}</option>
                </select>
              </div>
              
              <div className="flex items-center space-x-2">
                <Calendar className="h-5 w-5 text-gray-400" />
                <input
                  type="date"
                  value={filterDate}
                  onChange={(e) => setFilterDate(e.target.value)}
                  className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <button className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 px-3 py-2 rounded-lg transition-colors">
                <Download className="h-4 w-4" />
                <span>{t('common.export', 'Export')}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Sessions Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('common.patient', 'Patient')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('common.therapist', 'Therapist')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('sessions.type', 'Session Type')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('sessions.dateTime', 'Date & Time')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('common.status', 'Status')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('common.actions', 'Actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {currentSessions.length > 0 ? (
                  currentSessions.map((session) => (
                    <tr key={session.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                              {session.patientName.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {session.patientName}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              ID: {session.patientId}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-gray-400" />
                          <div className="text-sm text-gray-900 dark:text-white">
                            {session.therapistName}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {session.sessionType}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {session.date}
                        </div>
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <Clock className="h-4 w-4 mr-1" />
                          {session.time}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(session.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          {getStatusActions(session)}
                          <button
                            onClick={() => navigate(`/center/sessions/${session.id}`)}
                            className="text-blue-600 hover:text-blue-700 p-1 rounded transition-colors"
                            title="View Session"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => navigate(`/center/sessions/${session.id}/edit`)}
                            className="text-green-600 hover:text-green-700 p-1 rounded transition-colors"
                            title="Edit Session"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteSession(session.id)}
                            className="text-red-600 hover:text-red-700 p-1 rounded transition-colors"
                            title="Delete Session"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                      {searchTerm || filterStatus !== 'all' || filterDate
                        ? t('sessions.noResults', 'No sessions found matching your criteria')
                        : t('sessions.noSessions', 'No sessions found. Schedule your first session to get started.')
                      }
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  Showing {indexOfFirstSession + 1} to {Math.min(indexOfLastSession, filteredSessions.length)} of {filteredSessions.length} results
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Previous
                  </button>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Page {currentPage} of {totalPages}
                  </span>
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default SessionList;
