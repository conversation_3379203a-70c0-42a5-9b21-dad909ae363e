import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';

const LoadingSpinner = ({ 
  size = 'md', 
  text = null, 
  className = '',
  fullScreen = false 
}) => {
  const { t } = useLanguage();
  
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const spinner = (
    <div className={`spinner ${sizeClasses[size]} ${className}`} />
  );

  const content = (
    <div className="flex flex-col items-center justify-center gap-3">
      {spinner}
      {text && (
        <p className="text-sm text-muted-foreground animate-pulse">
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
        <div className="bg-card p-6 rounded-lg shadow-lg border">
          {content}
        </div>
      </div>
    );
  }

  return content;
};

export default LoadingSpinner;
