{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "add": "Add", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "clear": "Clear", "submit": "Submit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "yes": "Yes", "no": "No", "confirm": "Confirm", "view": "View", "download": "Download", "upload": "Upload", "refresh": "Refresh", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "profile": "Profile", "settings": "Settings", "dashboard": "Dashboard", "home": "Home", "about": "About", "contact": "Contact", "help": "Help", "support": "Support"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "signInWithGoogle": "Sign in with Google", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "welcomeBack": "Welcome back!", "welcomeToPhysioCenter": "Welcome to PhysioCenter", "signInToContinue": "Sign in to continue to your account", "createAccountToStart": "Create an account to get started", "invalidCredentials": "Invalid email or password", "accountCreated": "Account created successfully", "passwordResetSent": "Password reset email sent", "signInSuccessful": "Signed in successfully", "signOutSuccessful": "Signed out successfully"}, "navigation": {"dashboard": "Dashboard", "patients": "Patients", "sessions": "Sessions", "therapists": "Therapists", "centers": "Centers", "appointments": "Appointments", "reports": "Reports", "billing": "Billing", "settings": "Settings", "notifications": "Notifications", "profile": "Profile", "help": "Help & Support"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back, {{name}}!", "overview": "Overview", "todaysSessions": "Today's Sessions", "upcomingAppointments": "Upcoming Appointments", "recentPatients": "Recent Patients", "quickStats": "Quick Stats", "totalPatients": "Total Patients", "totalSessions": "Total Sessions", "todaysRevenue": "Today's Revenue", "monthlyRevenue": "Monthly Revenue", "activeTherapists": "Active Therapists", "pendingAppointments": "Pending Appointments", "noDataAvailable": "No data available", "viewAll": "View All", "addNew": "Add New"}, "patients": {"title": "Patients", "addPatient": "Add Patient", "editPatient": "<PERSON>", "patientDetails": "Patient Details", "patientList": "Patient List", "searchPatients": "Search patients...", "noPatients": "No patients found", "name": "Name", "age": "Age", "phone": "Phone", "email": "Email", "address": "Address", "medicalHistory": "Medical History", "emergencyContact": "Emergency Contact", "profilePhoto": "Profile Photo", "dateOfBirth": "Date of Birth", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "patientAdded": "Patient added successfully", "patientUpdated": "Patient updated successfully", "patientDeleted": "Patient deleted successfully", "deletePatientConfirm": "Are you sure you want to delete this patient?", "viewPatient": "View Patient", "deletePatient": "Delete Patient"}, "sessions": {"title": "Sessions", "addSession": "Add Session", "editSession": "Edit Session", "sessionDetails": "Session Details", "sessionList": "Session List", "searchSessions": "Search sessions...", "noSessions": "No sessions found", "patient": "Patient", "therapist": "Therapist", "date": "Date", "time": "Time", "duration": "Duration", "type": "Type", "status": "Status", "notes": "Notes", "progressNotes": "Progress Notes", "painScoreBefore": "Pain Score (Before)", "painScoreAfter": "Pain Score (After)", "scheduled": "Scheduled", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "noShow": "No Show", "consultation": "Consultation", "therapy": "Therapy", "followUp": "Follow Up", "sessionAdded": "Session added successfully", "sessionUpdated": "Session updated successfully", "sessionDeleted": "Session deleted successfully", "deleteSessionConfirm": "Are you sure you want to delete this session?", "markAsCompleted": "<PERSON> as Completed", "markAsCancelled": "<PERSON> as Cancelled"}, "therapists": {"title": "Therapists", "addTherapist": "Add Therapist", "editTherapist": "Edit Therapist", "therapistDetails": "Therapist Details", "therapistList": "Therapist List", "searchTherapists": "Search therapists...", "noTherapists": "No therapists found", "name": "Name", "email": "Email", "phone": "Phone", "specialization": "Specialization", "experience": "Experience (Years)", "licenseNumber": "License Number", "isActive": "Active", "therapistAdded": "Therapist added successfully", "therapistUpdated": "Therapist updated successfully", "therapistDeleted": "Therapist deleted successfully", "deleteTherapistConfirm": "Are you sure you want to delete this therapist?"}, "centers": {"title": "Centers", "addCenter": "Add Center", "editCenter": "Edit Center", "centerDetails": "Center Details", "centerList": "Center List", "searchCenters": "Search centers...", "noCenters": "No centers found", "name": "Name", "address": "Address", "phone": "Phone", "email": "Email", "licenseNumber": "License Number", "licenseExpiry": "License Expiry", "subscriptionPlan": "Subscription Plan", "subscriptionStart": "Subscription Start", "subscriptionEnd": "Subscription End", "isActive": "Active", "trial": "Trial", "basic": "Basic", "pro": "Pro", "centerAdded": "Center added successfully", "centerUpdated": "Center updated successfully", "centerDeleted": "Center deleted successfully", "deleteCenterConfirm": "Are you sure you want to delete this center?"}, "notifications": {"title": "Notifications", "markAsRead": "<PERSON> <PERSON>", "markAllAsRead": "<PERSON> as <PERSON>", "noNotifications": "No notifications", "newNotification": "New notification", "appointmentReminder": "Appointment Reminder", "sessionReminder": "Session Reminder", "paymentDue": "Payment Due", "systemUpdate": "System Update", "notificationSent": "Notification sent successfully", "allNotificationsRead": "All notifications marked as read"}, "subscription": {"title": "Subscription", "currentPlan": "Current Plan", "upgradePlan": "Upgrade Plan", "billingHistory": "Billing History", "paymentMethod": "Payment Method", "nextBilling": "Next Billing", "planFeatures": "Plan Features", "choosePlan": "Choose <PERSON>", "monthly": "Monthly", "yearly": "Yearly", "save": "Save {{percentage}}%", "mostPopular": "Most Popular", "currentPlanLabel": "Current Plan", "upgradeNow": "Upgrade Now", "subscriptionExpired": "Your subscription has expired", "subscriptionExpiringSoon": "Your subscription expires in {{days}} days", "renewSubscription": "Renew Subscription"}, "settings": {"title": "Settings", "general": "General", "account": "Account", "notifications": "Notifications", "security": "Security", "billing": "Billing", "preferences": "Preferences", "language": "Language", "theme": "Theme", "lightMode": "Light Mode", "darkMode": "Dark Mode", "systemMode": "System Mode", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "smsNotifications": "SMS Notifications", "changePassword": "Change Password", "twoFactorAuth": "Two-Factor Authentication", "deleteAccount": "Delete Account", "settingsSaved": "Setting<PERSON> saved successfully"}, "errors": {"generic": "Something went wrong. Please try again.", "networkError": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access denied.", "notFound": "The requested resource was not found.", "validationError": "Please check your input and try again.", "serverError": "Server error. Please try again later.", "sessionExpired": "Your session has expired. Please sign in again.", "subscriptionRequired": "This feature requires an active subscription.", "subscriptionExpired": "Your subscription has expired. Please renew to continue."}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be no more than {{max}} characters", "numeric": "Must be a number", "positiveNumber": "Must be a positive number", "dateInvalid": "Please enter a valid date", "passwordMismatch": "Passwords do not match", "fileSize": "File size must be less than {{size}}MB", "fileType": "Invalid file type. Allowed types: {{types}}"}}