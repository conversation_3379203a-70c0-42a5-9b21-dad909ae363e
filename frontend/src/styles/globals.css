@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for theming */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}

/* Base styles */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Component styles */
@layer components {
  /* Card components */
  .card {
    @apply rounded-lg border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-gray-600 dark:text-gray-400;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Button components */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-default {
    @apply bg-blue-600 text-white hover:bg-blue-700;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2;
  }

  .btn-secondary {
    @apply bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 hover:bg-gray-300 dark:hover:bg-gray-600 h-10 px-4 py-2;
  }

  .btn-destructive {
    @apply bg-red-600 text-white hover:bg-red-700 h-10 px-4 py-2;
  }

  .btn-outline {
    @apply border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 h-10 px-4 py-2;
  }

  .btn-ghost {
    @apply hover:bg-gray-100 dark:hover:bg-gray-700 h-10 px-4 py-2;
  }

  .btn-link {
    @apply text-blue-600 underline-offset-4 hover:underline h-10 px-4 py-2;
  }

  .btn-sm {
    @apply h-9 rounded-md px-3;
  }

  .btn-lg {
    @apply h-11 rounded-md px-8;
  }

  /* Input components */
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Form components */
  .form-label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }

  .form-description {
    @apply text-sm text-gray-600 dark:text-gray-400;
  }

  .form-message {
    @apply text-sm font-medium text-red-600 dark:text-red-400;
  }

  /* Badge components */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .badge-default {
    @apply border-transparent bg-blue-600 text-white hover:bg-blue-700;
  }

  .badge-secondary {
    @apply border-transparent bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 hover:bg-gray-300 dark:hover:bg-gray-600;
  }

  .badge-destructive {
    @apply border-transparent bg-red-600 text-white hover:bg-red-700;
  }

  .badge-outline {
    @apply text-gray-900 dark:text-gray-100;
  }

  /* Navigation components */
  .nav-link {
    @apply flex items-center gap-3 rounded-lg px-3 py-2 text-gray-600 dark:text-gray-400 transition-all hover:text-blue-600 hover:bg-gray-100 dark:hover:bg-gray-700;
  }

  .nav-link.active {
    @apply bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400;
  }

  /* Table components */
  .table {
    @apply w-full caption-bottom text-sm;
  }

  .table-header {
    @apply [&_tr]:border-b;
  }

  .table-body {
    @apply [&_tr:last-child]:border-0;
  }

  .table-footer {
    @apply border-t bg-gray-100/50 dark:bg-gray-800/50 font-medium;
  }

  .table-row {
    @apply border-b transition-colors hover:bg-gray-100/50 dark:hover:bg-gray-800/50;
  }

  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-gray-600 dark:text-gray-400;
  }

  .table-cell {
    @apply p-4 align-middle [&:has([role=checkbox])]:pr-0;
  }

  .table-caption {
    @apply mt-4 text-sm text-gray-600 dark:text-gray-400;
  }
}

/* Utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(243 244 246);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Loading spinner */
.spinner {
  border: 2px solid hsl(var(--muted));
  border-top: 2px solid hsl(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Focus styles */
.focus-visible {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .card-content {
    @apply p-4;
  }
  
  .card-header {
    @apply p-4;
  }
  
  .table-cell {
    @apply p-2;
  }
  
  .table-head {
    @apply h-10 px-2;
  }
}
