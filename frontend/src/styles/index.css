@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors */
    --background: 255 255 255;
    --foreground: 15 23 42;
    --card: 255 255 255;
    --card-foreground: 15 23 42;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;
    --primary: 37 99 235;
    --primary-foreground: 255 255 255;
    --secondary: 241 245 249;
    --secondary-foreground: 15 23 42;
    --muted: 241 245 249;
    --muted-foreground: 100 116 139;
    --accent: 241 245 249;
    --accent-foreground: 15 23 42;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 37 99 235;
    --radius: 0.5rem;
    
    /* Toast colors */
    --toast-bg: rgb(255 255 255);
    --toast-color: rgb(15 23 42);
    --toast-border: rgb(226 232 240);
  }

  .dark {
    /* Dark theme colors */
    --background: 2 6 23;
    --foreground: 248 250 252;
    --card: 2 6 23;
    --card-foreground: 248 250 252;
    --popover: 2 6 23;
    --popover-foreground: 248 250 252;
    --primary: 59 130 246;
    --primary-foreground: 15 23 42;
    --secondary: 30 41 59;
    --secondary-foreground: 248 250 252;
    --muted: 30 41 59;
    --muted-foreground: 148 163 184;
    --accent: 30 41 59;
    --accent-foreground: 248 250 252;
    --destructive: 220 38 38;
    --destructive-foreground: 248 250 252;
    --border: 30 41 59;
    --input: 30 41 59;
    --ring: 59 130 246;
    
    /* Toast colors */
    --toast-bg: rgb(30 41 59);
    --toast-color: rgb(248 250 252);
    --toast-border: rgb(51 65 85);
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-muted-foreground/20 border-t-primary;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-ring disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply btn bg-primary text-primary-foreground hover:bg-primary/90;
  }

  .btn-secondary {
    @apply btn bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .btn-outline {
    @apply btn border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }

  .btn-ghost {
    @apply btn hover:bg-accent hover:text-accent-foreground;
  }

  .btn-destructive {
    @apply btn bg-destructive text-destructive-foreground hover:bg-destructive/90;
  }

  /* Size variants */
  .btn-sm {
    @apply h-9 px-3;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-11 px-8;
  }

  /* Input styles */
  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-ring disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Card styles */
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-default {
    @apply badge border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
  }

  .badge-secondary {
    @apply badge border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .badge-destructive {
    @apply badge border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80;
  }

  .badge-outline {
    @apply badge text-foreground;
  }

  /* Alert styles */
  .alert {
    @apply relative w-full rounded-lg border p-4;
  }

  .alert-destructive {
    @apply alert border-destructive/50 text-destructive dark:border-destructive;
  }

  /* Navigation styles */
  .nav-link {
    @apply flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary hover:bg-accent;
  }

  .nav-link.active {
    @apply bg-accent text-accent-foreground;
  }

  /* Form styles */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }

  .form-error {
    @apply text-sm font-medium text-destructive;
  }

  /* Table styles */
  .table {
    @apply w-full caption-bottom text-sm;
  }

  .table-header {
    @apply border-b;
  }

  .table-row {
    @apply border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted;
  }

  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-muted-foreground;
  }

  .table-cell {
    @apply p-4 align-middle;
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Animation utilities */
  .animate-in {
    animation-duration: 0.2s;
    animation-fill-mode: both;
  }

  .animate-out {
    animation-duration: 0.15s;
    animation-fill-mode: both;
  }

  /* Layout utilities */
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-8 sm:py-12 lg:py-16;
  }

  /* Gradient utilities */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary/80;
  }

  .gradient-secondary {
    @apply bg-gradient-to-r from-secondary to-secondary/80;
  }
}
