import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  signInWithGoogle as firebaseSignInWithGoogle,
  signOut as firebaseSignOut,
  onAuthStateChange,
  getCurrentUserToken,
  getFirebaseErrorMessage,
  auth
} from '../lib/firebase';
import toast from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [firebaseUser, setFirebaseUser] = useState(null);

  // Initialize auth state listener
  useEffect(() => {
    const unsubscribe = onAuthStateChange(async (firebaseUser) => {
      setFirebaseUser(firebaseUser);
      
      if (firebaseUser) {
        try {
          // Get Firebase ID token
          const idToken = await getIdToken(firebaseUser);
          
          // Get FCM token if supported
          let fcmToken = null;
          if (messaging) {
            try {
              fcmToken = await getToken(messaging, {
                vapidKey: import.meta.env.VITE_FIREBASE_VAPID_KEY
              });
            } catch (fcmError) {
              console.warn('Failed to get FCM token:', fcmError);
            }
          }
          
          // Login/register with backend
          const response = await authService.login(idToken, fcmToken);
          setUser(response.user);
          
          if (response.isNewUser) {
            toast.success('Welcome to PhysioCenter!');
          }
        } catch (error) {
          console.error('Authentication error:', error);
          toast.error('Authentication failed. Please try again.');
          setUser(null);
        }
      } else {
        setUser(null);
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const signInWithGoogle = async (role = 'CENTER_OWNER') => {
    try {
      setLoading(true);

      // Sign in with Google using our Firebase service
      const result = await firebaseSignInWithGoogle();
      const firebaseUser = result.user;
      const idToken = result.idToken;
      
      // Login/register with backend
      const response = await fetch('http://localhost:3000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firebaseToken: idToken,
          role: role
        }),
      });

      if (!response.ok) {
        throw new Error('Backend authentication failed');
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Authentication failed');
      }
      setUser(data.user);

      if (data.isNewUser) {
        toast.success('Account created successfully!');
      } else {
        toast.success('Signed in successfully!');
      }

      return data;
    } catch (error) {
      console.error('Google sign-in error:', error);

      const errorMessage = getFirebaseErrorMessage(error);
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      
      // Sign out from Firebase
      await firebaseSignOut();
      
      setUser(null);
      setFirebaseUser(null);
      
      toast.success('Signed out successfully!');
    } catch (error) {
      console.error('Sign-out error:', error);
      toast.error('Sign-out failed. Please try again.');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (profileData) => {
    try {
      if (!user) {
        throw new Error('No user logged in');
      }
      
      const updatedUser = await authService.updateProfile(profileData);
      setUser(updatedUser);
      
      toast.success('Profile updated successfully!');
      return updatedUser;
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error('Failed to update profile. Please try again.');
      throw error;
    }
  };

  const refreshToken = async () => {
    try {
      if (!firebaseUser) {
        throw new Error('No Firebase user');
      }
      
      const idToken = await getIdToken(firebaseUser, true); // Force refresh
      return idToken;
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  };

  const getAuthToken = async () => {
    try {
      if (!firebaseUser) {
        return null;
      }
      
      const idToken = await getIdToken(firebaseUser);
      return idToken;
    } catch (error) {
      console.error('Get auth token error:', error);
      return null;
    }
  };

  // Check if user has specific role
  const hasRole = (role) => {
    return user?.role === role;
  };

  // Check if user has any of the specified roles
  const hasAnyRole = (roles) => {
    return roles.includes(user?.role);
  };

  // Check if user is center owner
  const isCenterOwner = () => hasRole('CENTER_OWNER');
  
  // Check if user is therapist
  const isTherapist = () => hasRole('THERAPIST');
  
  // Check if user is patient
  const isPatient = () => hasRole('PATIENT');
  
  // Check if user is super admin
  const isSuperAdmin = () => hasRole('SUPER_ADMIN');

  // Check if user has active subscription
  const hasActiveSubscription = () => {
    if (!user) return false;
    
    // For center owners, check their centers
    if (user.role === 'CENTER_OWNER' && user.ownedCenters?.length > 0) {
      return user.ownedCenters.some(center => {
        if (!center.subscriptionEnd) return true; // No end date means active
        return new Date(center.subscriptionEnd) > new Date();
      });
    }
    
    // For therapists, check their center's subscription
    if (user.role === 'THERAPIST' && user.therapistProfile?.center) {
      const center = user.therapistProfile.center;
      if (!center.subscriptionEnd) return true;
      return new Date(center.subscriptionEnd) > new Date();
    }
    
    // Patients and super admins always have access
    return user.role === 'PATIENT' || user.role === 'SUPER_ADMIN';
  };

  const value = {
    user,
    firebaseUser,
    loading,
    signInWithGoogle,
    signOut,
    updateProfile,
    refreshToken,
    getAuthToken,
    hasRole,
    hasAnyRole,
    isCenterOwner,
    isTherapist,
    isPatient,
    isSuperAdmin,
    hasActiveSubscription,
    isAuthenticated: !!user,
    isFirebaseAuthenticated: !!firebaseUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
