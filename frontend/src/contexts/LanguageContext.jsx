import React, { createContext, useContext, useState, useEffect } from 'react';

// Import language files
import enTranslations from '../i18n/en.json';
import bnTranslations from '../i18n/bn.json';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

const translations = {
  en: enTranslations,
  bn: bnTranslations
};

const supportedLanguages = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'bn', name: 'Bengali', nativeName: 'বাংলা' }
];

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    // Check localStorage first
    const savedLanguage = localStorage.getItem('physio-language');
    if (savedLanguage && translations[savedLanguage]) {
      return savedLanguage;
    }
    
    // Check browser language
    const browserLanguage = navigator.language.split('-')[0];
    if (translations[browserLanguage]) {
      return browserLanguage;
    }
    
    // Default to English
    return 'en';
  });

  useEffect(() => {
    // Save to localStorage
    localStorage.setItem('physio-language', language);
    
    // Set document language
    document.documentElement.lang = language;
    
    // Set document direction (for RTL languages if needed)
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
  }, [language]);

  const changeLanguage = (newLanguage) => {
    if (translations[newLanguage]) {
      setLanguage(newLanguage);
    } else {
      console.warn(`Language ${newLanguage} is not supported`);
    }
  };

  const t = (key, params = {}) => {
    const keys = key.split('.');
    let value = translations[language];
    
    // Navigate through nested keys
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // Fallback to English if key not found in current language
        value = translations.en;
        for (const fallbackKey of keys) {
          if (value && typeof value === 'object' && fallbackKey in value) {
            value = value[fallbackKey];
          } else {
            console.warn(`Translation key "${key}" not found in ${language} or English`);
            return key; // Return the key itself as fallback
          }
        }
        break;
      }
    }
    
    // If value is not a string, return the key
    if (typeof value !== 'string') {
      console.warn(`Translation key "${key}" does not resolve to a string`);
      return key;
    }
    
    // Replace parameters in the translation
    let result = value;
    Object.keys(params).forEach(param => {
      const placeholder = `{{${param}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), params[param]);
    });
    
    return result;
  };

  const getCurrentLanguage = () => {
    return supportedLanguages.find(lang => lang.code === language) || supportedLanguages[0];
  };

  const isRTL = () => {
    return ['ar', 'he', 'fa'].includes(language);
  };

  const formatCurrency = (amount, currency = 'BDT') => {
    if (currency === 'BDT') {
      return `৳${amount.toLocaleString(language === 'bn' ? 'bn-BD' : 'en-US')}`;
    }
    
    try {
      return new Intl.NumberFormat(language === 'bn' ? 'bn-BD' : 'en-US', {
        style: 'currency',
        currency: currency
      }).format(amount);
    } catch (error) {
      console.warn(`Currency formatting failed for ${currency}:`, error);
      return `${currency} ${amount}`;
    }
  };

  const formatDate = (date, options = {}) => {
    const defaultOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    
    const formatOptions = { ...defaultOptions, ...options };
    
    try {
      return new Intl.DateTimeFormat(language === 'bn' ? 'bn-BD' : 'en-US', formatOptions).format(new Date(date));
    } catch (error) {
      console.warn('Date formatting failed:', error);
      return new Date(date).toLocaleDateString();
    }
  };

  const formatTime = (date, options = {}) => {
    const defaultOptions = {
      hour: '2-digit',
      minute: '2-digit'
    };
    
    const formatOptions = { ...defaultOptions, ...options };
    
    try {
      return new Intl.DateTimeFormat(language === 'bn' ? 'bn-BD' : 'en-US', formatOptions).format(new Date(date));
    } catch (error) {
      console.warn('Time formatting failed:', error);
      return new Date(date).toLocaleTimeString();
    }
  };

  const value = {
    language,
    changeLanguage,
    t,
    supportedLanguages,
    getCurrentLanguage,
    isRTL,
    formatCurrency,
    formatDate,
    formatTime,
    isEnglish: language === 'en',
    isBengali: language === 'bn'
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
