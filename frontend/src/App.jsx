import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';
import { useTheme } from './contexts/ThemeContext';

// Layout components
import AuthLayout from './components/layouts/AuthLayout';
import DashboardLayout from './components/layouts/DashboardLayout';

// Page components
import LoginPage from './pages/auth/LoginPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import PatientsPage from './pages/patients/PatientsPage';
import SessionsPage from './pages/sessions/SessionsPage';
import TherapistsPage from './pages/therapists/TherapistsPage';
import CentersPage from './pages/centers/CentersPage';
import NotificationsPage from './pages/notifications/NotificationsPage';
import SettingsPage from './pages/settings/SettingsPage';
import ProfilePage from './pages/profile/ProfilePage';

// Admin components
import AdminLogin from './components/admin/AdminLogin';
import AdminDashboard from './components/admin/AdminDashboard';

// Center Owner components
import CenterOwnerLogin from './components/center/CenterOwnerLogin';
import CenterOwnerDashboard from './components/center/CenterOwnerDashboard';

// Loading component
import LoadingSpinner from './components/ui/LoadingSpinner';

// Protected route component
const ProtectedRoute = ({ children, requiredRoles = [] }) => {
  const { user, loading, hasAnyRole } = useAuth();
  
  if (loading) {
    return <LoadingSpinner />;
  }
  
  if (!user) {
    return <Navigate to="/login" replace />;
  }
  
  if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return children;
};

// Public route component (redirect to dashboard if authenticated)
const PublicRoute = ({ children }) => {
  const { user, loading } = useAuth();
  
  if (loading) {
    return <LoadingSpinner />;
  }
  
  if (user) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return children;
};

function App() {
  const { loading } = useAuth();
  const { theme } = useTheme();
  
  // Show loading spinner while initializing auth
  if (loading) {
    return <LoadingSpinner />;
  }
  
  return (
    <div className={`min-h-screen bg-background text-foreground ${theme}`}>
      <Routes>
        {/* Admin routes */}
        <Route path="/admin/login" element={<AdminLogin />} />
        <Route path="/admin/dashboard" element={<AdminDashboard />} />

        {/* Center Owner routes */}
        <Route path="/center/login" element={<CenterOwnerLogin />} />
        <Route path="/center/dashboard" element={<CenterOwnerDashboard />} />

        {/* Public routes */}
        <Route path="/login" element={
          <PublicRoute>
            <AuthLayout>
              <LoginPage />
            </AuthLayout>
          </PublicRoute>
        } />

        {/* Protected routes */}
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <DashboardLayout>
              <DashboardPage />
            </DashboardLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/patients" element={
          <ProtectedRoute requiredRoles={['CENTER_OWNER', 'THERAPIST', 'SUPER_ADMIN']}>
            <DashboardLayout>
              <PatientsPage />
            </DashboardLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/sessions" element={
          <ProtectedRoute>
            <DashboardLayout>
              <SessionsPage />
            </DashboardLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/therapists" element={
          <ProtectedRoute requiredRoles={['CENTER_OWNER', 'SUPER_ADMIN']}>
            <DashboardLayout>
              <TherapistsPage />
            </DashboardLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/centers" element={
          <ProtectedRoute requiredRoles={['SUPER_ADMIN']}>
            <DashboardLayout>
              <CentersPage />
            </DashboardLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/notifications" element={
          <ProtectedRoute>
            <DashboardLayout>
              <NotificationsPage />
            </DashboardLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/settings" element={
          <ProtectedRoute>
            <DashboardLayout>
              <SettingsPage />
            </DashboardLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/profile" element={
          <ProtectedRoute>
            <DashboardLayout>
              <ProfilePage />
            </DashboardLayout>
          </ProtectedRoute>
        } />
        
        {/* Default redirect */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        
        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </div>
  );
}

export default App;
