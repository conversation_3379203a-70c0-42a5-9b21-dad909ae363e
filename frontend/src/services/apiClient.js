import axios from 'axios';
import { auth } from '../config/firebase';
import { getIdToken } from 'firebase/auth';
import toast from 'react-hot-toast';

// Create axios instance
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config) => {
    try {
      // Get current Firebase user
      const currentUser = auth.currentUser;
      
      if (currentUser) {
        // Get fresh ID token
        const idToken = await getIdToken(currentUser);
        config.headers.Authorization = `Bearer ${idToken}`;
      }
      
      return config;
    } catch (error) {
      console.error('Error adding auth token to request:', error);
      return config;
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response) {
      const { status, data } = error.response;
      
      // Handle authentication errors
      if (status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        
        try {
          // Try to refresh token
          const currentUser = auth.currentUser;
          if (currentUser) {
            const idToken = await getIdToken(currentUser, true); // Force refresh
            originalRequest.headers.Authorization = `Bearer ${idToken}`;
            return apiClient(originalRequest);
          } else {
            // No user, redirect to login
            window.location.href = '/login';
            return Promise.reject(error);
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          // Redirect to login
          window.location.href = '/login';
          return Promise.reject(error);
        }
      }
      
      // Handle other HTTP errors
      switch (status) {
        case 400:
          console.error('Bad Request:', data.message);
          break;
        case 403:
          toast.error(data.message || 'Access denied');
          break;
        case 404:
          console.error('Not Found:', data.message);
          break;
        case 409:
          toast.error(data.message || 'Conflict occurred');
          break;
        case 422:
          console.error('Validation Error:', data.message);
          break;
        case 429:
          toast.error('Too many requests. Please try again later.');
          break;
        case 500:
          toast.error('Server error. Please try again later.');
          break;
        case 502:
        case 503:
        case 504:
          toast.error('Service temporarily unavailable. Please try again later.');
          break;
        default:
          console.error('API Error:', data.message || 'Unknown error occurred');
      }
    } else if (error.request) {
      // Network error
      console.error('Network Error:', error.message);
      toast.error('Network error. Please check your connection.');
    } else {
      // Other error
      console.error('Request Error:', error.message);
    }
    
    return Promise.reject(error);
  }
);

// Helper functions for different HTTP methods
const api = {
  get: (url, config = {}) => apiClient.get(url, config),
  post: (url, data = {}, config = {}) => apiClient.post(url, data, config),
  put: (url, data = {}, config = {}) => apiClient.put(url, data, config),
  patch: (url, data = {}, config = {}) => apiClient.patch(url, data, config),
  delete: (url, config = {}) => apiClient.delete(url, config),
};

// File upload helper
const uploadFile = async (url, file, onProgress = null) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };
  
  if (onProgress) {
    config.onUploadProgress = (progressEvent) => {
      const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress(percentCompleted);
    };
  }
  
  return apiClient.post(url, formData, config);
};

// Download file helper
const downloadFile = async (url, filename = null) => {
  try {
    const response = await apiClient.get(url, {
      responseType: 'blob',
    });
    
    // Create blob link to download
    const blob = new Blob([response.data]);
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    
    // Set filename
    if (filename) {
      link.download = filename;
    } else {
      // Try to get filename from response headers
      const contentDisposition = response.headers['content-disposition'];
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          link.download = filenameMatch[1];
        }
      }
    }
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    window.URL.revokeObjectURL(link.href);
    
    return response;
  } catch (error) {
    console.error('Download error:', error);
    toast.error('Failed to download file');
    throw error;
  }
};

// Request cancellation helper
const createCancelToken = () => {
  return axios.CancelToken.source();
};

// Check if error is cancellation
const isCancel = (error) => {
  return axios.isCancel(error);
};

export { 
  apiClient, 
  api, 
  uploadFile, 
  downloadFile, 
  createCancelToken, 
  isCancel 
};
