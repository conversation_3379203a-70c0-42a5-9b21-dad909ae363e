import { apiClient } from './apiClient';

class AuthService {
  /**
   * Login or register user with Firebase token
   * @param {string} idToken - Firebase ID token
   * @param {string} fcmToken - FCM token for push notifications
   * @param {string} role - User role for new users
   * @returns {Promise<Object>} User data and login status
   */
  async login(idToken, fcmToken = null, role = 'CENTER_OWNER') {
    try {
      const response = await apiClient.post('/auth/login', {
        idToken,
        fcmToken,
        role
      });
      
      return response.data;
    } catch (error) {
      console.error('Login API error:', error);
      throw this.handleAuthError(error);
    }
  }

  /**
   * Get current user profile
   * @returns {Promise<Object>} User profile data
   */
  async getProfile() {
    try {
      const response = await apiClient.get('/auth/profile');
      return response.data.user;
    } catch (error) {
      console.error('Get profile API error:', error);
      throw this.handleAuthError(error);
    }
  }

  /**
   * Update user profile
   * @param {Object} profileData - Profile data to update
   * @returns {Promise<Object>} Updated user data
   */
  async updateProfile(profileData) {
    try {
      const response = await apiClient.put('/auth/profile', profileData);
      return response.data.user;
    } catch (error) {
      console.error('Update profile API error:', error);
      throw this.handleAuthError(error);
    }
  }

  /**
   * Logout user
   * @returns {Promise<void>}
   */
  async logout() {
    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout API error:', error);
      // Don't throw error for logout as it should always succeed locally
    }
  }

  /**
   * Handle authentication errors
   * @param {Error} error - API error
   * @returns {Error} Formatted error
   */
  handleAuthError(error) {
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          return new Error(data.message || 'Authentication failed');
        case 403:
          return new Error(data.message || 'Access denied');
        case 404:
          return new Error(data.message || 'User not found');
        case 409:
          return new Error(data.message || 'User already exists');
        case 422:
          return new Error(data.message || 'Invalid data provided');
        case 500:
          return new Error('Server error. Please try again later.');
        default:
          return new Error(data.message || 'Authentication failed');
      }
    } else if (error.request) {
      return new Error('Network error. Please check your connection.');
    } else {
      return new Error(error.message || 'Authentication failed');
    }
  }
}

export const authService = new AuthService();
