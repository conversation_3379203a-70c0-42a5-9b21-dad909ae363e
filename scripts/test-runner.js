#!/usr/bin/env node

import { execSync, spawn } from 'child_process';
import { existsSync } from 'fs';
import path from 'path';

/**
 * Comprehensive test runner for PhysioCenter
 * Sets up backend, frontend, and runs all tests
 */

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'blue');
  
  // Check if backend .env exists
  if (!existsSync('backend/.env')) {
    log('❌ Backend .env file not found!', 'red');
    log('Please copy backend/.env.example to backend/.env and configure it.', 'yellow');
    process.exit(1);
  }
  
  // Check if frontend .env exists
  if (!existsSync('frontend/.env')) {
    log('⚠️  Frontend .env file not found. Using defaults.', 'yellow');
  }
  
  log('✅ Prerequisites check passed', 'green');
}

function setupBackend() {
  log('🔧 Setting up backend...', 'blue');
  
  try {
    // Install backend dependencies
    log('📦 Installing backend dependencies...', 'cyan');
    execSync('bun install', { cwd: 'backend', stdio: 'inherit' });
    
    // Setup development environment
    log('🗄️  Setting up database...', 'cyan');
    execSync('bun run setup:dev', { cwd: 'backend', stdio: 'inherit' });
    
    log('✅ Backend setup complete', 'green');
  } catch (error) {
    log('❌ Backend setup failed', 'red');
    console.error(error.message);
    process.exit(1);
  }
}

function setupFrontend() {
  log('🔧 Setting up frontend...', 'blue');
  
  try {
    // Install frontend dependencies
    log('📦 Installing frontend dependencies...', 'cyan');
    execSync('npm install', { cwd: 'frontend', stdio: 'inherit' });
    
    log('✅ Frontend setup complete', 'green');
  } catch (error) {
    log('❌ Frontend setup failed', 'red');
    console.error(error.message);
    process.exit(1);
  }
}

function setupTests() {
  log('🔧 Setting up tests...', 'blue');
  
  try {
    // Install test dependencies
    log('📦 Installing test dependencies...', 'cyan');
    execSync('npm install', { cwd: 'tests', stdio: 'inherit' });
    
    // Install Playwright browsers
    log('🌐 Installing Playwright browsers...', 'cyan');
    execSync('npx playwright install', { cwd: 'tests', stdio: 'inherit' });
    
    log('✅ Test setup complete', 'green');
  } catch (error) {
    log('❌ Test setup failed', 'red');
    console.error(error.message);
    process.exit(1);
  }
}

function startServers() {
  log('🚀 Starting servers...', 'blue');
  
  return new Promise((resolve, reject) => {
    // Start backend server
    log('🔧 Starting backend server...', 'cyan');
    const backend = spawn('bun', ['run', 'dev'], { 
      cwd: 'backend',
      stdio: 'pipe'
    });
    
    // Start frontend server
    log('🎨 Starting frontend server...', 'cyan');
    const frontend = spawn('npm', ['run', 'dev'], { 
      cwd: 'frontend',
      stdio: 'pipe'
    });
    
    // Wait for servers to be ready
    let backendReady = false;
    let frontendReady = false;
    
    backend.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Server listening on')) {
        backendReady = true;
        log('✅ Backend server ready', 'green');
        checkBothReady();
      }
    });
    
    frontend.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Local:') || output.includes('ready')) {
        frontendReady = true;
        log('✅ Frontend server ready', 'green');
        checkBothReady();
      }
    });
    
    function checkBothReady() {
      if (backendReady && frontendReady) {
        resolve({ backend, frontend });
      }
    }
    
    // Timeout after 2 minutes
    setTimeout(() => {
      reject(new Error('Servers failed to start within timeout'));
    }, 120000);
  });
}

function runTests() {
  log('🧪 Running tests...', 'blue');
  
  try {
    // Run API tests
    log('🔌 Running API tests...', 'cyan');
    execSync('npx playwright test tests/api --reporter=line', { 
      cwd: 'tests', 
      stdio: 'inherit' 
    });
    
    // Run frontend tests
    log('🎨 Running frontend tests...', 'cyan');
    execSync('npx playwright test tests/frontend --reporter=line', { 
      cwd: 'tests', 
      stdio: 'inherit' 
    });
    
    // Run E2E tests
    log('🔄 Running E2E tests...', 'cyan');
    execSync('npx playwright test tests/e2e --reporter=line', { 
      cwd: 'tests', 
      stdio: 'inherit' 
    });
    
    log('✅ All tests completed successfully!', 'green');
    
    // Generate test report
    log('📊 Generating test report...', 'cyan');
    execSync('npx playwright show-report', { cwd: 'tests' });
    
  } catch (error) {
    log('❌ Some tests failed', 'red');
    console.error(error.message);
    return false;
  }
  
  return true;
}

async function main() {
  try {
    log('🚀 PhysioCenter Test Runner', 'magenta');
    log('================================', 'magenta');
    
    // Check prerequisites
    checkPrerequisites();
    
    // Setup components
    setupBackend();
    setupFrontend();
    setupTests();
    
    // Start servers
    const { backend, frontend } = await startServers();
    
    // Wait a bit for servers to fully initialize
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Run tests
    const success = runTests();
    
    // Cleanup
    log('🧹 Cleaning up...', 'blue');
    backend.kill();
    frontend.kill();
    
    if (success) {
      log('🎉 All tests passed! PhysioCenter is working correctly.', 'green');
      process.exit(0);
    } else {
      log('❌ Some tests failed. Please check the output above.', 'red');
      process.exit(1);
    }
    
  } catch (error) {
    log('❌ Test runner failed:', 'red');
    console.error(error.message);
    process.exit(1);
  }
}

// Run if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
