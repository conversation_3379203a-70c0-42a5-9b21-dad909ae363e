#!/usr/bin/env node

import { execSync } from 'child_process';
import { existsSync, copyFileSync } from 'fs';
import { join } from 'path';

/**
 * Project setup script for PhysioCenter
 * Sets up the entire project from scratch
 */

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function setupEnvironmentFiles() {
  log('📝 Setting up environment files...', 'blue');
  
  // Backend .env
  if (!existsSync('backend/.env')) {
    if (existsSync('backend/.env.example')) {
      copyFileSync('backend/.env.example', 'backend/.env');
      log('✅ Created backend/.env from example', 'green');
      log('⚠️  Please edit backend/.env with your configuration', 'yellow');
    } else {
      log('❌ backend/.env.example not found', 'red');
    }
  } else {
    log('✅ backend/.env already exists', 'green');
  }
  
  // Frontend .env
  if (!existsSync('frontend/.env')) {
    if (existsSync('frontend/.env.example')) {
      copyFileSync('frontend/.env.example', 'frontend/.env');
      log('✅ Created frontend/.env from example', 'green');
      log('⚠️  Please edit frontend/.env with your Firebase configuration', 'yellow');
    } else {
      log('❌ frontend/.env.example not found', 'red');
    }
  } else {
    log('✅ frontend/.env already exists', 'green');
  }
}

function installDependencies() {
  log('📦 Installing dependencies...', 'blue');
  
  try {
    // Backend dependencies
    log('🔧 Installing backend dependencies...', 'cyan');
    execSync('bun install', { cwd: 'backend', stdio: 'inherit' });
    
    // Frontend dependencies
    log('🎨 Installing frontend dependencies...', 'cyan');
    execSync('npm install', { cwd: 'frontend', stdio: 'inherit' });
    
    // Test dependencies
    log('🧪 Installing test dependencies...', 'cyan');
    execSync('npm install', { cwd: 'tests', stdio: 'inherit' });
    
    log('✅ All dependencies installed successfully', 'green');
  } catch (error) {
    log('❌ Failed to install dependencies', 'red');
    console.error(error.message);
    process.exit(1);
  }
}

function setupDatabase() {
  log('🗄️  Setting up database...', 'blue');
  
  try {
    // Generate Prisma client
    log('🔧 Generating Prisma client...', 'cyan');
    execSync('bunx prisma generate', { cwd: 'backend', stdio: 'inherit' });
    
    // Note: We don't run db push here as it requires a valid database connection
    log('⚠️  Database schema ready. Run "bun run setup:dev" in backend/ when database is configured', 'yellow');
    
  } catch (error) {
    log('❌ Database setup failed', 'red');
    console.error(error.message);
  }
}

function setupPlaywright() {
  log('🎭 Setting up Playwright...', 'blue');
  
  try {
    log('🌐 Installing Playwright browsers...', 'cyan');
    execSync('npx playwright install', { cwd: 'tests', stdio: 'inherit' });
    
    log('✅ Playwright setup complete', 'green');
  } catch (error) {
    log('❌ Playwright setup failed', 'red');
    console.error(error.message);
  }
}

function displayNextSteps() {
  log('\n🎉 Project setup complete!', 'green');
  log('================================', 'magenta');
  
  log('\n📋 Next Steps:', 'blue');
  log('1. Configure your environment files:', 'cyan');
  log('   - Edit backend/.env with your database and Firebase configuration', 'yellow');
  log('   - Edit frontend/.env with your Firebase configuration', 'yellow');
  
  log('\n2. Set up your database:', 'cyan');
  log('   cd backend && bun run setup:dev', 'yellow');
  
  log('\n3. Start development servers:', 'cyan');
  log('   # Terminal 1 - Backend', 'yellow');
  log('   cd backend && bun run dev', 'yellow');
  log('   # Terminal 2 - Frontend', 'yellow');
  log('   cd frontend && npm run dev', 'yellow');
  
  log('\n4. Run tests:', 'cyan');
  log('   node scripts/test-runner.js', 'yellow');
  
  log('\n🌐 URLs:', 'blue');
  log('   - Frontend: http://localhost:5173', 'cyan');
  log('   - Backend API: http://localhost:3000', 'cyan');
  log('   - API Docs: http://localhost:3000/docs', 'cyan');
  
  log('\n📚 Documentation:', 'blue');
  log('   - README.md - Complete project documentation', 'cyan');
  log('   - backend/README.md - Backend-specific documentation', 'cyan');
  log('   - frontend/README.md - Frontend-specific documentation', 'cyan');
  
  log('\n🔑 Test Accounts (after seeding):', 'blue');
  log('   - Super Admin: <EMAIL>', 'cyan');
  log('   - Various center owners, therapists, and patients', 'cyan');
  
  log('\n💡 Tips:', 'blue');
  log('   - Use "bun run db:studio" to view database in browser', 'cyan');
  log('   - Check logs for any configuration issues', 'cyan');
  log('   - Refer to subscription plans in README.md', 'cyan');
}

function main() {
  try {
    log('🚀 PhysioCenter Project Setup', 'magenta');
    log('==============================', 'magenta');
    
    setupEnvironmentFiles();
    installDependencies();
    setupDatabase();
    setupPlaywright();
    displayNextSteps();
    
  } catch (error) {
    log('❌ Project setup failed:', 'red');
    console.error(error.message);
    process.exit(1);
  }
}

// Run if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
