{"name": "physio-center-tests", "version": "1.0.0", "description": "End-to-end tests for PhysioCenter application", "type": "module", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:api": "playwright test tests/api", "test:frontend": "playwright test tests/frontend", "test:e2e": "playwright test tests/e2e", "test:report": "playwright show-report", "install:browsers": "playwright install"}, "devDependencies": {"@playwright/test": "^1.40.0"}, "engines": {"node": ">=18.0.0"}}