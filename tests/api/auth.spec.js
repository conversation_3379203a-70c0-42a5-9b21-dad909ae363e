import { test, expect } from '@playwright/test';

test.describe('Authentication API', () => {
  test('should reject requests without authentication', async ({ request }) => {
    const response = await request.get('/api/auth/profile');
    
    expect(response.status()).toBe(401);
    
    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toBe('Unauthorized');
  });

  test('should reject invalid Firebase tokens', async ({ request }) => {
    const response = await request.post('/api/auth/login', {
      data: {
        idToken: 'invalid-token',
        role: 'CENTER_OWNER'
      }
    });
    
    expect(response.status()).toBe(401);
    
    const data = await response.json();
    expect(data).toHaveProperty('error');
  });

  test('should validate login request body', async ({ request }) => {
    const response = await request.post('/api/auth/login', {
      data: {
        // Missing required idToken
        role: 'CENTER_OWNER'
      }
    });
    
    expect(response.status()).toBe(400);
  });

  test('should validate role in login request', async ({ request }) => {
    const response = await request.post('/api/auth/login', {
      data: {
        idToken: 'some-token',
        role: 'INVALID_ROLE'
      }
    });
    
    expect(response.status()).toBe(400);
  });
});
