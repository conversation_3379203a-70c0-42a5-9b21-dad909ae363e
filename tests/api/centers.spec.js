import { test, expect } from '@playwright/test';

test.describe('Centers API', () => {
  const mockToken = 'mock-firebase-token';

  test('should require authentication for center operations', async ({ request }) => {
    // Test GET /api/centers without auth
    const getResponse = await request.get('/api/centers');
    expect(getResponse.status()).toBe(401);

    // Test POST /api/centers without auth
    const postResponse = await request.post('/api/centers', {
      data: {
        name: 'Test Center',
        address: 'Test Address'
      }
    });
    expect(postResponse.status()).toBe(401);
  });

  test('should validate center creation data', async ({ request }) => {
    const response = await request.post('/api/centers', {
      headers: {
        'Authorization': `Bearer ${mockToken}`
      },
      data: {
        // Missing required fields
        name: ''
      }
    });
    
    // Should return validation error (400) or auth error (401)
    expect([400, 401]).toContain(response.status());
  });

  test('should handle center listing for admin', async ({ request }) => {
    const response = await request.get('/api/centers', {
      headers: {
        'Authorization': `Bearer ${mockToken}`
      }
    });
    
    // Should return auth error since we don't have valid token
    expect(response.status()).toBe(401);
  });

  test('should handle center creation with valid data', async ({ request }) => {
    const response = await request.post('/api/centers', {
      headers: {
        'Authorization': `Bearer ${mockToken}`
      },
      data: {
        name: 'Test Physiotherapy Center',
        address: '123 Test Street, Test City',
        phone: '+**********',
        email: '<EMAIL>',
        licenseNumber: 'LIC123456'
      }
    });
    
    // Should return auth error since we don't have valid token
    expect(response.status()).toBe(401);
  });

  test('should handle multi-branch creation restrictions', async ({ request }) => {
    // Test creating a branch (should require PRO plan)
    const response = await request.post('/api/centers', {
      headers: {
        'Authorization': `Bearer ${mockToken}`
      },
      data: {
        name: 'Branch Center',
        address: '456 Branch Street',
        parentCenterId: 'some-parent-id',
        isMainBranch: false
      }
    });
    
    // Should return auth error since we don't have valid token
    expect(response.status()).toBe(401);
  });
});
