import { test, expect } from '@playwright/test';

test.describe('Health Check API', () => {
  test('should return health status', async ({ request }) => {
    const response = await request.get('/health');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('status', 'ok');
    expect(data).toHaveProperty('timestamp');
    expect(data).toHaveProperty('uptime');
    expect(data).toHaveProperty('version');
  });

  test('should return database status', async ({ request }) => {
    const response = await request.get('/health');
    const data = await response.json();
    
    expect(data).toHaveProperty('database');
    expect(data.database).toHaveProperty('status', 'connected');
  });
});
