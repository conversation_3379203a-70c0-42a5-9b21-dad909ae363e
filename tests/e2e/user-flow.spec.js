import { test, expect } from '@playwright/test';

test.describe('Complete User Flow', () => {
  test('should complete the full user journey from login to dashboard', async ({ page }) => {
    // Step 1: Navigate to the application
    await page.goto('http://localhost:5173');
    
    // Should redirect to login page
    await expect(page).toHaveURL(/.*\/login/);
    
    // Step 2: Verify login page elements
    await expect(page.getByRole('heading', { name: /Welcome to PhysioCenter/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /Sign in with Google/i })).toBeVisible();
    
    // Step 3: Test role selection
    const centerOwnerRadio = page.getByRole('radio', { name: /Center Owner/i });
    await expect(centerOwnerRadio).toBeChecked();
    
    // Step 4: Verify features are displayed
    await expect(page.getByText('Patient management and records')).toBeVisible();
    await expect(page.getByText('Multi-branch support (Pro plan)')).toBeVisible();
    await expect(page.getByText('HRM and accounting modules')).toBeVisible();
    
    // Step 5: Test theme toggle functionality
    const themeToggle = page.getByRole('button', { name: /Toggle theme/i });
    await themeToggle.click();
    
    // Step 6: Test language switcher
    const languageButton = page.getByRole('button').filter({ hasText: /EN|BN/i });
    await expect(languageButton).toBeVisible();
    
    // Note: Actual Google authentication would require special setup
    // In a real test environment, you'd mock the authentication flow
  });

  test('should handle navigation properly', async ({ page }) => {
    // Test direct navigation to protected routes
    await page.goto('http://localhost:5173/dashboard');
    
    // Should redirect to login
    await expect(page).toHaveURL(/.*\/login/);
    
    // Test other protected routes
    await page.goto('http://localhost:5173/patients');
    await expect(page).toHaveURL(/.*\/login/);
    
    await page.goto('http://localhost:5173/sessions');
    await expect(page).toHaveURL(/.*\/login/);
  });

  test('should display proper error handling', async ({ page }) => {
    await page.goto('http://localhost:5173/non-existent-route');
    
    // Should redirect to dashboard, which then redirects to login
    await expect(page).toHaveURL(/.*\/login/);
  });
});
