import { test, expect } from '@playwright/test';

test.describe('Login Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:5173/login');
  });

  test('should display login page correctly', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/PhysioCenter/);
    
    // Check main heading
    await expect(page.getByRole('heading', { name: /Welcome to PhysioCenter/i })).toBeVisible();
    
    // Check role selection
    await expect(page.getByText('Center Owner')).toBeVisible();
    await expect(page.getByText('Therapist')).toBeVisible();
    await expect(page.getByText('Patient')).toBeVisible();
    
    // Check sign in button
    await expect(page.getByRole('button', { name: /Sign in with Google/i })).toBeVisible();
  });

  test('should allow role selection', async ({ page }) => {
    // Default selection should be Center Owner
    const centerOwnerRadio = page.getByRole('radio', { name: /Center Owner/i });
    await expect(centerOwnerRadio).toBeChecked();
    
    // Select Therapist role
    const therapistRadio = page.getByRole('radio', { name: /Therapist/i });
    await therapistRadio.click();
    await expect(therapistRadio).toBeChecked();
    await expect(centerOwnerRadio).not.toBeChecked();
    
    // Select Patient role
    const patientRadio = page.getByRole('radio', { name: /Patient/i });
    await patientRadio.click();
    await expect(patientRadio).toBeChecked();
    await expect(therapistRadio).not.toBeChecked();
  });

  test('should display features preview', async ({ page }) => {
    await expect(page.getByText('What you get with PhysioCenter:')).toBeVisible();
    await expect(page.getByText('Patient management and records')).toBeVisible();
    await expect(page.getByText('Appointment scheduling')).toBeVisible();
    await expect(page.getByText('Progress tracking and reports')).toBeVisible();
    await expect(page.getByText('Multi-branch support (Pro plan)')).toBeVisible();
    await expect(page.getByText('HRM and accounting modules')).toBeVisible();
  });

  test('should display trial information', async ({ page }) => {
    await expect(page.getByText('Free 30-day trial')).toBeVisible();
    await expect(page.getByText('Start with a free trial. No credit card required.')).toBeVisible();
  });

  test('should have theme toggle', async ({ page }) => {
    const themeToggle = page.getByRole('button', { name: /Toggle theme/i });
    await expect(themeToggle).toBeVisible();
    
    // Click theme toggle
    await themeToggle.click();
    
    // Check if theme changed (this would need more specific implementation)
    // For now, just verify the button is still there
    await expect(themeToggle).toBeVisible();
  });

  test('should have language switcher', async ({ page }) => {
    const languageButton = page.getByRole('button').filter({ hasText: /EN|BN/i });
    await expect(languageButton).toBeVisible();
  });

  test('should redirect authenticated users', async ({ page }) => {
    // This test would need proper authentication mocking
    // For now, we'll just verify the login page loads for unauthenticated users
    await expect(page.getByRole('heading', { name: /Welcome to PhysioCenter/i })).toBeVisible();
  });
});
