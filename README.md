# PhysioCenter SaaS

A comprehensive physiotherapy center management system with multi-branch support, HRM, and accounting modules.

## 🌟 Features

### Core Features (All Plans)
- **Patient Management**: Complete patient records and medical history
- **Session Management**: Therapy session scheduling and tracking
- **Appointment System**: Easy appointment booking and management
- **Progress Tracking**: Monitor patient progress with detailed reports
- **Notifications**: Real-time notifications via Firebase Cloud Messaging
- **Multi-language Support**: English and Bengali language support
- **Dark/Light Theme**: Customizable user interface themes

### Premium Features
- **Multi-branch Support** (PRO+ plans): Manage multiple center locations
- **HRM Module** (BASIC+ plans): Employee management, attendance, leave, payroll
- **Accounting Module** (PRO+ plans): Expense tracking, invoicing, purchase orders
- **Advanced Reports** (BASIC+ plans): Detailed analytics and insights
- **API Access** (PRO+ plans): REST API for integrations
- **Custom Branding** (PRO+ plans): White-label customization

## 🏗️ Architecture

### Backend
- **Runtime**: Bun.js for high performance
- **Framework**: Fastify with TypeScript support
- **Database**: MySQL with Prisma ORM
- **Authentication**: Firebase Authentication
- **Push Notifications**: Firebase Cloud Messaging
- **API Documentation**: Swagger/OpenAPI
- **Security**: CORS, Helmet, Rate limiting

### Frontend
- **Framework**: React 18 with Vite
- **Styling**: Tailwind CSS with custom design system
- **State Management**: React Query for server state
- **Authentication**: Firebase Auth integration
- **Routing**: React Router v6
- **UI Components**: Custom component library
- **Internationalization**: Multi-language support

### Database Schema
- **Users**: Firebase-authenticated users with roles
- **Centers**: Physiotherapy centers with subscription plans
- **Patients**: Patient records and medical history
- **Therapists**: Therapist profiles and specializations
- **Sessions**: Therapy sessions and appointments
- **Employees**: HRM employee records (BASIC+ plans)
- **Accounting**: Expenses, invoices, payments (PRO+ plans)

## 🚀 Quick Start

### Prerequisites
- [Bun.js](https://bun.sh/) (v1.0.0 or higher)
- [Node.js](https://nodejs.org/) (v18.0.0 or higher)
- MySQL database
- Firebase project with Admin SDK

### 1. Clone the Repository
```bash
git clone <repository-url>
cd physio-center
```

### 2. Backend Setup
```bash
cd backend
cp .env.example .env
# Edit .env with your configuration
bun run setup:dev
```

### 3. Frontend Setup
```bash
cd frontend
cp .env.example .env
# Edit .env with your Firebase configuration
npm install
```

### 4. Start Development Servers
```bash
# Backend (Terminal 1)
cd backend && bun run dev

# Frontend (Terminal 2)
cd frontend && npm run dev
```

### 5. Run Tests
```bash
# Run comprehensive test suite
node scripts/test-runner.js

# Or run tests individually
cd tests && npm test
```

## 📋 Subscription Plans

| Feature | Trial | Basic | Pro | Enterprise |
|---------|-------|-------|-----|------------|
| **Duration** | 30 days | Unlimited | Unlimited | Unlimited |
| **Price (BDT/month)** | Free | ৳1,500 | ৳3,000 | ৳5,000 |
| **Max Branches** | 1 | 1 | 5 | Unlimited |
| **Max Patients** | 10 | 100 | 500 | Unlimited |
| **Max Therapists** | 2 | 5 | 20 | Unlimited |
| **Max Employees** | 3 | 10 | 50 | Unlimited |
| **HRM Module** | ❌ | ✅ | ✅ | ✅ |
| **Accounting Module** | ❌ | ❌ | ✅ | ✅ |
| **Multi-branch** | ❌ | ❌ | ✅ | ✅ |
| **Advanced Reports** | ❌ | ✅ | ✅ | ✅ |
| **API Access** | ❌ | ❌ | ✅ | ✅ |
| **Priority Support** | ❌ | ❌ | ✅ | ✅ |

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
DATABASE_URL="mysql://user:password@localhost:3306/physio_center"
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY=your_private_key
FIREBASE_CLIENT_EMAIL=your_client_email
PORT=3000
NODE_ENV=development
```

#### Frontend (.env)
```env
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_API_BASE_URL=http://localhost:3000/api
```

## 🧪 Testing

The project includes comprehensive testing with Playwright:

### Test Types
- **API Tests**: Backend endpoint testing
- **Frontend Tests**: React component testing
- **E2E Tests**: Complete user flow testing
- **Subscription Tests**: Feature restriction validation

### Running Tests
```bash
# Run all tests
npm run test

# Run specific test suites
npm run test:api
npm run test:frontend
npm run test:e2e

# Run tests with UI
npm run test:ui

# Debug tests
npm run test:debug
```

## 📚 API Documentation

When running in development mode, API documentation is available at:
- **Swagger UI**: http://localhost:3000/docs
- **Health Check**: http://localhost:3000/health

### Key Endpoints
- `POST /api/auth/login` - Authentication
- `GET /api/centers` - Center management
- `GET /api/patients` - Patient management
- `GET /api/sessions` - Session management
- `GET /api/hrm/employees` - Employee management (BASIC+)
- `GET /api/accounting/expenses` - Expense management (PRO+)

## 🔒 Security

- Firebase Authentication with JWT tokens
- Role-based access control (RBAC)
- Subscription-based feature restrictions
- Rate limiting and request validation
- CORS and security headers
- Input sanitization and validation

## 🌍 Internationalization

The application supports multiple languages:
- **English** (en) - Default
- **Bengali** (bn) - বাংলা

Language files are located in `frontend/src/i18n/`.

## 🚀 Deployment

### Backend Deployment
```bash
cd backend
bun run build
bun run start
```

### Frontend Deployment
```bash
cd frontend
npm run build
# Deploy dist/ folder to your hosting provider
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: [Project Wiki](link-to-wiki)

## 🎯 Roadmap

- [ ] Mobile app (React Native)
- [ ] Telemedicine integration
- [ ] Advanced analytics dashboard
- [ ] Third-party integrations
- [ ] Automated backup system
- [ ] Multi-tenant architecture
