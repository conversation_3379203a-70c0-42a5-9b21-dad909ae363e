{"date": "Sat Jul 05 2025 10:06:50 GMT+0600 (Bangladesh Standard Time)", "environment": "development", "error": {}, "exception": true, "level": "error", "message": "uncaughtException: unable to determine transport target for \"pino-pretty\"\nError: unable to determine transport target for \"pino-pretty\"\n    at fixTarget (/home/<USER>/Desktop/node/physio/backend/node_modules/pino/lib/transport.js:160:17)\n    at transport (/home/<USER>/Desktop/node/physio/backend/node_modules/pino/lib/transport.js:130:22)\n    at normalizeArgs (/home/<USER>/Desktop/node/physio/backend/node_modules/pino/lib/tools.js:312:16)\n    at pino (/home/<USER>/Desktop/node/physio/backend/node_modules/pino/pino.js:91:28)\n    at createPinoLogger (/home/<USER>/Desktop/node/physio/backend/node_modules/fastify/lib/logger.js:42:14)\n    at createLogger (/home/<USER>/Desktop/node/physio/backend/node_modules/fastify/lib/logger.js:101:18)\n    at fastify (/home/<USER>/Desktop/node/physio/backend/node_modules/fastify/fastify.js:135:33)\n    at /home/<USER>/Desktop/node/physio/backend/src/server.js:41:17\n    at moduleEvaluation (native:1:11)\n    at moduleEvaluation (native:1:11)\n    at loadAndEvaluateModule (native:2)\n    at processTicksAndRejections (native:7:39)", "os": {"loadavg": [5.6, 3.65, 3.36], "uptime": 50237}, "process": {"argv": ["/home/<USER>/.bun/bin/bun", "/home/<USER>/Desktop/node/physio/backend/src/server.js"], "cwd": "/home/<USER>/Desktop/node/physio/backend", "execPath": "/home/<USER>/.bun/bin/bun", "gid": 1000, "memoryUsage": {"arrayBuffers": 491, "external": 3353294, "heapTotal": 9014272, "heapUsed": 9297086, "rss": 96641024}, "pid": 151652, "uid": 1000, "version": "v22.6.0"}, "service": "physio-center-backend", "stack": "Error: unable to determine transport target for \"pino-pretty\"\n    at fixTarget (/home/<USER>/Desktop/node/physio/backend/node_modules/pino/lib/transport.js:160:17)\n    at transport (/home/<USER>/Desktop/node/physio/backend/node_modules/pino/lib/transport.js:130:22)\n    at normalizeArgs (/home/<USER>/Desktop/node/physio/backend/node_modules/pino/lib/tools.js:312:16)\n    at pino (/home/<USER>/Desktop/node/physio/backend/node_modules/pino/pino.js:91:28)\n    at createPinoLogger (/home/<USER>/Desktop/node/physio/backend/node_modules/fastify/lib/logger.js:42:14)\n    at createLogger (/home/<USER>/Desktop/node/physio/backend/node_modules/fastify/lib/logger.js:101:18)\n    at fastify (/home/<USER>/Desktop/node/physio/backend/node_modules/fastify/fastify.js:135:33)\n    at /home/<USER>/Desktop/node/physio/backend/src/server.js:41:17\n    at moduleEvaluation (native:1:11)\n    at moduleEvaluation (native:1:11)\n    at loadAndEvaluateModule (native:2)\n    at processTicksAndRejections (native:7:39)", "timestamp": "2025-07-05 10:06:50", "trace": [{"column": 17, "file": "/home/<USER>/Desktop/node/physio/backend/node_modules/pino/lib/transport.js", "function": "fixTarget", "line": 160, "method": null, "native": false}, {"column": 22, "file": "/home/<USER>/Desktop/node/physio/backend/node_modules/pino/lib/transport.js", "function": "transport", "line": 130, "method": null, "native": false}, {"column": 16, "file": "/home/<USER>/Desktop/node/physio/backend/node_modules/pino/lib/tools.js", "function": "normalizeArgs", "line": 312, "method": null, "native": false}, {"column": 28, "file": "/home/<USER>/Desktop/node/physio/backend/node_modules/pino/pino.js", "function": "pino", "line": 91, "method": null, "native": false}, {"column": 14, "file": "/home/<USER>/Desktop/node/physio/backend/node_modules/fastify/lib/logger.js", "function": "createPinoLogger", "line": 42, "method": null, "native": false}, {"column": 18, "file": "/home/<USER>/Desktop/node/physio/backend/node_modules/fastify/lib/logger.js", "function": "createLogger", "line": 101, "method": null, "native": false}, {"column": 33, "file": "/home/<USER>/Desktop/node/physio/backend/node_modules/fastify/fastify.js", "function": "fastify", "line": 135, "method": null, "native": false}, {"column": 17, "file": "/home/<USER>/Desktop/node/physio/backend/src/server.js", "function": null, "line": 41, "method": null, "native": false}, {"column": 11, "file": "native", "function": "moduleEvaluation", "line": 1, "method": null, "native": false}, {"column": 11, "file": "native", "function": "moduleEvaluation", "line": 1, "method": null, "native": false}, {"column": null, "file": "native", "function": "loadAndEvaluateModule", "line": 2, "method": null, "native": false}, {"column": 39, "file": "native", "function": "processTicksAndRejections", "line": 7, "method": null, "native": false}]}