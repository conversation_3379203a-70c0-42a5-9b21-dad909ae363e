# PhysioCenter Backend API

A Fastify-based backend API for the PhysioCenter SaaS application, built with Bun.js runtime.

## Features

- **Authentication**: Firebase Authentication with JWT tokens
- **Database**: MySQL with Prisma ORM
- **API Documentation**: Swagger/OpenAPI integration
- **Security**: CORS, Helmet, Rate limiting
- **File Upload**: Multipart file upload support
- **Logging**: Winston-based structured logging
- **Push Notifications**: Firebase Cloud Messaging (FCM)
- **Role-based Access Control**: Multi-role authorization system

## Tech Stack

- **Runtime**: Bun.js
- **Framework**: Fastify
- **Database**: MySQL
- **ORM**: Prisma
- **Authentication**: Firebase Admin SDK
- **Validation**: Zod schemas
- **Logging**: Winston
- **Documentation**: Swagger UI

## Prerequisites

- [Bun.js](https://bun.sh/) (v1.0.0 or higher)
- MySQL database
- Firebase project with Admin SDK credentials

## Installation

1. **Install dependencies**:
   ```bash
   cd backend
   bun install
   ```

2. **Set up environment variables**:
   - Copy `.env.example` to `.env`
   - Fill in your database and Firebase credentials
   - Update other configuration as needed

3. **Set up the database**:
   ```bash
   # Generate Prisma client
   bun run db:generate
   
   # Push schema to database (for development)
   bun run db:push
   
   # Or run migrations (for production)
   bun run db:migrate
   ```

4. **Start the development server**:
   ```bash
   bun run dev
   ```

The server will start on `http://localhost:3000` by default.

## Environment Variables

### Required Variables

- `DATABASE_URL`: MySQL connection string
- `FIREBASE_PROJECT_ID`: Firebase project ID
- `FIREBASE_PRIVATE_KEY`: Firebase private key
- `FIREBASE_CLIENT_EMAIL`: Firebase client email

### Optional Variables

- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment (development/production)
- `JWT_SECRET`: JWT signing secret
- `CORS_ORIGIN`: Allowed CORS origins
- `LOG_LEVEL`: Logging level (debug/info/warn/error)

See `.env.example` for a complete list of available variables.

## API Documentation

When `ENABLE_API_DOCS=true`, Swagger UI is available at:
- **Development**: http://localhost:3000/docs
- **Health Check**: http://localhost:3000/health

## Database Schema

The application uses the following main entities:

- **Users**: Firebase-authenticated users with roles
- **Centers**: Physiotherapy centers
- **Therapists**: Therapist profiles linked to centers
- **Patients**: Patient records
- **Sessions**: Therapy sessions/appointments
- **Notifications**: Push notifications

## User Roles

- **SUPER_ADMIN**: Full system access
- **CENTER_OWNER**: Manage their own centers
- **THERAPIST**: Access assigned center data
- **PATIENT**: Access own patient data

## API Endpoints

### Authentication
- `POST /api/auth/login` - Login/register with Firebase token
- `GET /api/auth/profile` - Get current user profile
- `PUT /api/auth/profile` - Update user profile
- `POST /api/auth/logout` - Logout (clear FCM token)

### Centers
- `GET /api/centers` - List centers (admin only)
- `POST /api/centers` - Create center
- `GET /api/centers/:id` - Get center details
- `PUT /api/centers/:id` - Update center
- `DELETE /api/centers/:id` - Delete center

### Patients
- `GET /api/patients` - List patients
- `POST /api/patients` - Create patient
- `GET /api/patients/:id` - Get patient details
- `PUT /api/patients/:id` - Update patient
- `DELETE /api/patients/:id` - Delete patient

### Sessions
- `GET /api/sessions` - List sessions
- `POST /api/sessions` - Create session
- `GET /api/sessions/:id` - Get session details
- `PUT /api/sessions/:id` - Update session
- `DELETE /api/sessions/:id` - Delete session

### Notifications
- `GET /api/notifications` - Get user notifications
- `PUT /api/notifications/:id/read` - Mark as read

### Subscriptions
- `GET /api/subscriptions/plans` - Get available plans
- `GET /api/subscriptions/current` - Get current subscription
- `POST /api/subscriptions/upgrade` - Upgrade subscription

## Development

### Scripts

- `bun run dev` - Start development server with hot reload
- `bun run start` - Start production server
- `bun run build` - Build for production
- `bun run test` - Run tests
- `bun run lint` - Run ESLint
- `bun run format` - Format code with Prettier

### Database Commands

- `bun run db:generate` - Generate Prisma client
- `bun run db:push` - Push schema to database
- `bun run db:migrate` - Run database migrations
- `bun run db:studio` - Open Prisma Studio
- `bun run db:seed` - Seed database with sample data

## Deployment

1. **Build the application**:
   ```bash
   bun run build
   ```

2. **Set production environment variables**

3. **Run database migrations**:
   ```bash
   bun run db:migrate
   ```

4. **Start the server**:
   ```bash
   bun run start
   ```

## Security

- All routes require authentication except public endpoints
- Role-based access control for different user types
- Rate limiting to prevent abuse
- CORS configuration for cross-origin requests
- Helmet for security headers
- Input validation with Zod schemas

## Logging

Logs are written to:
- Console (development)
- `logs/combined.log` (all logs)
- `logs/error.log` (errors only)
- `logs/exceptions.log` (uncaught exceptions)

## Error Handling

The API returns consistent error responses:

```json
{
  "error": "Error Type",
  "message": "Human-readable error message",
  "statusCode": 400,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License
