{"name": "physio-center-backend", "version": "1.0.0", "description": "PhysioCenter SaaS Backend API", "main": "src/server.js", "type": "module", "scripts": {"dev": "bun --watch src/server.js", "start": "bun src/server.js", "build": "bun build src/server.js --outdir dist --target bun", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "bun src/scripts/seed.js", "setup:dev": "bun src/scripts/setup-dev.js", "test": "bun test", "lint": "eslint src/", "format": "prettier --write src/"}, "keywords": ["fastify", "physio", "saas", "mysql", "firebase"], "author": "PhysioCenter Team", "license": "MIT", "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/multipart": "^8.0.0", "@fastify/rate-limit": "^9.1.0", "@fastify/static": "^7.0.1", "@fastify/swagger": "^8.14.0", "@fastify/swagger-ui": "^2.1.0", "@prisma/client": "^5.7.1", "fastify": "^4.25.2", "firebase-admin": "^12.0.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "zod": "^3.22.4", "nodemailer": "^6.9.8", "cron": "^3.1.6", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.10.6", "prisma": "^5.7.1", "eslint": "^8.56.0", "prettier": "^3.1.1", "bun-types": "latest", "@faker-js/faker": "^8.3.1"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}