import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

// Helper function to generate random date within range
function randomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// Helper function to generate random decimal
function randomDecimal(min, max, decimals = 2) {
  const value = Math.random() * (max - min) + min;
  return parseFloat(value.toFixed(decimals));
}

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user first
  console.log('👑 Creating admin user...');
  const adminEmail = '<EMAIL>';
  const adminPassword = 'Admin@123456';

  const existingAdmin = await prisma.admin.findUnique({
    where: { email: adminEmail }
  });

  if (!existingAdmin) {
    const hashedPassword = await bcrypt.hash(adminPassword, 12);

    const admin = await prisma.admin.create({
      data: {
        email: adminEmail,
        password: hashedPassword,
        name: 'System Administrator',
        isActive: true
      }
    });

    console.log('✅ Admin user created successfully!');
    console.log('\n🔐 ADMIN CREDENTIALS:');
    console.log(`📧 Email: ${adminEmail}`);
    console.log(`🔑 Password: ${adminPassword}`);
    console.log('⚠️  Please change the password after first login!\n');
  } else {
    console.log('ℹ️  Admin user already exists');
  }

  // Clear existing data
  console.log('🧹 Cleaning existing data...');
  await prisma.payrollRecord.deleteMany();
  await prisma.leaveRequest.deleteMany();
  await prisma.attendance.deleteMany();
  await prisma.employee.deleteMany();
  await prisma.payment.deleteMany();   
  await prisma.invoiceItem.deleteMany();
  await prisma.invoice.deleteMany();
  await prisma.purchaseItem.deleteMany();
  await prisma.purchase.deleteMany();
  await prisma.expense.deleteMany();
  await prisma.notification.deleteMany();
  await prisma.session.deleteMany();
  await prisma.patient.deleteMany();
  await prisma.therapist.deleteMany();
  await prisma.center.deleteMany();
  await prisma.user.deleteMany();

  // Create Super Admin User
  console.log('👤 Creating super admin user...');
  const superAdmin = await prisma.user.create({
    data: {
      firebaseUid: 'super-admin-uid',
      email: '<EMAIL>',
      displayName: 'Super Admin',
      role: 'SUPER_ADMIN',
      isActive: true,
    },
  });

  // Create Center Owners
  console.log('🏢 Creating center owners...');
  const centerOwners = [];
  for (let i = 0; i < 5; i++) {
    const owner = await prisma.user.create({
      data: {
        firebaseUid: `center-owner-${i + 1}`,
        email: faker.internet.email(),
        displayName: faker.person.fullName(),
        role: 'CENTER_OWNER',
        isActive: true,
        fcmToken: faker.string.alphanumeric(152),
      },
    });
    centerOwners.push(owner);
  }

  // Create Centers with different subscription plans
  console.log('🏥 Creating centers...');
  const centers = [];
  const subscriptionPlans = ['TRIAL', 'BASIC', 'PRO', 'ENTERPRISE'];
  
  for (let i = 0; i < centerOwners.length; i++) {
    const owner = centerOwners[i];
    const plan = subscriptionPlans[i % subscriptionPlans.length];
    
    // Create main center
    const center = await prisma.center.create({
      data: {
        name: `${faker.company.name()} Physiotherapy Center`,
        address: faker.location.streetAddress({ useFullAddress: true }),
        phone: faker.phone.number(),
        email: faker.internet.email(),
        licenseNumber: `LIC-${faker.string.alphanumeric(8).toUpperCase()}`,
        licenseExpiry: faker.date.future({ years: 2 }),
        subscriptionPlan: plan,
        subscriptionStart: faker.date.past({ years: 1 }),
        subscriptionEnd: plan === 'TRIAL' ? faker.date.future({ days: 30 }) : faker.date.future({ years: 1 }),
        isActive: true,
        isMainBranch: true,
        branchCode: `BR-${String(i + 1).padStart(3, '0')}`,
        maxBranches: plan === 'TRIAL' ? 1 : plan === 'BASIC' ? 1 : plan === 'PRO' ? 5 : 10,
        maxPatients: plan === 'TRIAL' ? 10 : plan === 'BASIC' ? 100 : plan === 'PRO' ? 500 : 1000,
        maxTherapists: plan === 'TRIAL' ? 2 : plan === 'BASIC' ? 5 : plan === 'PRO' ? 20 : 50,
        maxEmployees: plan === 'TRIAL' ? 3 : plan === 'BASIC' ? 10 : plan === 'PRO' ? 50 : 100,
        ownerId: owner.id,
      },
    });
    centers.push(center);

    // Create branches for PRO and ENTERPRISE plans
    if (plan === 'PRO' || plan === 'ENTERPRISE') {
      const branchCount = plan === 'PRO' ? 2 : 3;
      for (let j = 0; j < branchCount; j++) {
        const branch = await prisma.center.create({
          data: {
            name: `${center.name} - Branch ${j + 1}`,
            address: faker.location.streetAddress({ useFullAddress: true }),
            phone: faker.phone.number(),
            email: faker.internet.email(),
            licenseNumber: `LIC-${faker.string.alphanumeric(8).toUpperCase()}`,
            licenseExpiry: faker.date.future({ years: 2 }),
            subscriptionPlan: plan,
            subscriptionStart: center.subscriptionStart,
            subscriptionEnd: center.subscriptionEnd,
            isActive: true,
            isMainBranch: false,
            parentCenterId: center.id,
            branchCode: `BR-${String(i + 1).padStart(3, '0')}-${j + 1}`,
            maxBranches: center.maxBranches,
            maxPatients: center.maxPatients,
            maxTherapists: center.maxTherapists,
            maxEmployees: center.maxEmployees,
            ownerId: owner.id,
          },
        });
        centers.push(branch);
      }
    }
  }

  // Create Therapists
  console.log('👨‍⚕️ Creating therapists...');
  const therapists = [];
  for (const center of centers) {
    const therapistCount = Math.min(
      faker.number.int({ min: 1, max: 3 }),
      center.maxTherapists
    );
    
    for (let i = 0; i < therapistCount; i++) {
      const therapistUser = await prisma.user.create({
        data: {
          firebaseUid: `therapist-${center.id}-${i + 1}`,
          email: faker.internet.email(),
          displayName: faker.person.fullName(),
          role: 'THERAPIST',
          isActive: true,
          fcmToken: faker.string.alphanumeric(152),
        },
      });

      const therapist = await prisma.therapist.create({
        data: {
          specialization: faker.helpers.arrayElement([
            'Orthopedic Physiotherapy',
            'Neurological Physiotherapy',
            'Cardiopulmonary Physiotherapy',
            'Sports Physiotherapy',
            'Pediatric Physiotherapy',
            'Geriatric Physiotherapy',
          ]),
          licenseNumber: `TH-${faker.string.alphanumeric(8).toUpperCase()}`,
          experience: faker.number.int({ min: 1, max: 15 }),
          isActive: true,
          userId: therapistUser.id,
          centerId: center.id,
        },
      });
      therapists.push(therapist);
    }
  }

  // Create Patients
  console.log('🤕 Creating patients...');
  const patients = [];
  for (const center of centers) {
    const patientCount = Math.min(
      faker.number.int({ min: 5, max: 15 }),
      center.maxPatients
    );
    
    for (let i = 0; i < patientCount; i++) {
      const shouldHaveUser = faker.datatype.boolean(0.3); // 30% chance of having user account
      let patientUser = null;
      
      if (shouldHaveUser) {
        patientUser = await prisma.user.create({
          data: {
            firebaseUid: `patient-${center.id}-${i + 1}`,
            email: faker.internet.email(),
            displayName: faker.person.fullName(),
            role: 'PATIENT',
            isActive: true,
            fcmToken: faker.string.alphanumeric(152),
          },
        });
      }

      const patient = await prisma.patient.create({
        data: {
          name: patientUser?.displayName || faker.person.fullName(),
          age: faker.number.int({ min: 18, max: 80 }),
          phone: faker.phone.number(),
          address: faker.location.streetAddress({ useFullAddress: true }),
          medicalHistory: faker.lorem.paragraphs(2),
          emergencyContact: faker.phone.number(),
          isActive: true,
          userId: patientUser?.id,
          centerId: center.id,
        },
      });
      patients.push(patient);
    }
  }

  // Create Sessions
  console.log('📅 Creating sessions...');
  const sessions = [];
  for (const center of centers) {
    const centerTherapists = therapists.filter(t => t.centerId === center.id);
    const centerPatients = patients.filter(p => p.centerId === center.id);

    if (centerTherapists.length > 0 && centerPatients.length > 0) {
      const sessionCount = faker.number.int({ min: 10, max: 30 });

      for (let i = 0; i < sessionCount; i++) {
        const therapist = faker.helpers.arrayElement(centerTherapists);
        const patient = faker.helpers.arrayElement(centerPatients);
        const scheduledAt = randomDate(
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)  // 30 days from now
        );

        const session = await prisma.session.create({
          data: {
            type: faker.helpers.arrayElement(['CONSULTATION', 'THERAPY', 'FOLLOW_UP']),
            scheduledAt,
            duration: faker.helpers.arrayElement([30, 45, 60, 90]),
            status: faker.helpers.arrayElement(['SCHEDULED', 'COMPLETED', 'CANCELLED']),
            notes: faker.lorem.paragraph(),
            painScoreBefore: faker.number.int({ min: 1, max: 10 }),
            painScoreAfter: faker.number.int({ min: 1, max: 8 }),
            progressNotes: faker.lorem.paragraph(),
            centerId: center.id,
            patientId: patient.id,
            therapistId: therapist.id,
          },
        });
        sessions.push(session);
      }
    }
  }

  // Create Employees (for HRM module)
  console.log('👥 Creating employees...');
  const employees = [];
  for (const center of centers) {
    // Only create employees for centers with HRM access
    const hasHRM = ['BASIC', 'PRO', 'ENTERPRISE'].includes(center.subscriptionPlan);
    if (!hasHRM) continue;

    const employeeCount = Math.min(
      faker.number.int({ min: 3, max: 8 }),
      center.maxEmployees
    );

    for (let i = 0; i < employeeCount; i++) {
      const employee = await prisma.employee.create({
        data: {
          employeeId: `EMP-${center.branchCode}-${String(i + 1).padStart(3, '0')}`,
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          email: faker.internet.email(),
          phone: faker.phone.number(),
          address: faker.location.streetAddress({ useFullAddress: true }),
          dateOfBirth: faker.date.birthdate({ min: 22, max: 60, mode: 'age' }),
          hireDate: faker.date.past({ years: 3 }),
          position: faker.helpers.arrayElement([
            'Receptionist',
            'Administrative Assistant',
            'Billing Specialist',
            'Maintenance Staff',
            'Security Guard',
            'Cleaner',
            'IT Support',
          ]),
          department: faker.helpers.arrayElement([
            'Administration',
            'Operations',
            'Support',
            'Maintenance',
          ]),
          salary: randomDecimal(15000, 50000),
          employmentType: faker.helpers.arrayElement(['FULL_TIME', 'PART_TIME', 'CONTRACT']),
          status: faker.helpers.arrayElement(['ACTIVE', 'ACTIVE', 'ACTIVE', 'INACTIVE']), // 75% active
          emergencyContact: faker.person.fullName(),
          emergencyPhone: faker.phone.number(),
          centerId: center.id,
        },
      });
      employees.push(employee);
    }
  }

  // Create Attendance Records
  console.log('⏰ Creating attendance records...');
  const attendanceRecords = [];
  for (const employee of employees) {
    // Create attendance for last 30 days
    for (let i = 0; i < 30; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      // Skip weekends
      if (date.getDay() === 0 || date.getDay() === 6) continue;

      const checkIn = new Date(date);
      checkIn.setHours(faker.number.int({ min: 8, max: 10 }), faker.number.int({ min: 0, max: 59 }));

      const checkOut = new Date(checkIn);
      checkOut.setHours(checkIn.getHours() + 8, checkIn.getMinutes() + faker.number.int({ min: 0, max: 30 }));

      const attendance = await prisma.attendance.create({
        data: {
          date,
          checkIn,
          checkOut,
          totalHours: randomDecimal(7.5, 9),
          overtimeHours: Math.random() > 0.8 ? randomDecimal(0.5, 2) : 0,
          status: faker.helpers.arrayElement(['PRESENT', 'PRESENT', 'PRESENT', 'LATE']), // 75% present
          employeeId: employee.id,
        },
      });
      attendanceRecords.push(attendance);
    }
  }

  // Create Expenses (for Accounting module)
  console.log('💰 Creating expenses...');
  const expenses = [];
  for (const center of centers) {
    // Only create expenses for centers with accounting access
    const hasAccounting = ['PRO', 'ENTERPRISE'].includes(center.subscriptionPlan);
    if (!hasAccounting) continue;

    const expenseCount = faker.number.int({ min: 10, max: 25 });

    for (let i = 0; i < expenseCount; i++) {
      const expense = await prisma.expense.create({
        data: {
          expenseNumber: `EXP-${center.branchCode}-${String(i + 1).padStart(4, '0')}`,
          title: faker.helpers.arrayElement([
            'Office Supplies',
            'Equipment Purchase',
            'Utility Bills',
            'Rent Payment',
            'Marketing Campaign',
            'Professional Services',
            'Insurance Premium',
            'Maintenance & Repair',
          ]),
          description: faker.lorem.sentence(),
          category: faker.helpers.arrayElement([
            'OFFICE_SUPPLIES',
            'EQUIPMENT',
            'UTILITIES',
            'RENT',
            'MARKETING',
            'PROFESSIONAL_SERVICES',
            'INSURANCE',
            'MAINTENANCE',
          ]),
          amount: randomDecimal(500, 15000),
          expenseDate: faker.date.past({ years: 1 }),
          paymentMethod: faker.helpers.arrayElement([
            'CASH',
            'BANK_TRANSFER',
            'CREDIT_CARD',
            'MOBILE_BANKING',
          ]),
          vendor: faker.company.name(),
          status: faker.helpers.arrayElement(['PENDING', 'APPROVED', 'PAID']),
          centerId: center.id,
        },
      });
      expenses.push(expense);
    }
  }

  // Create Invoices
  console.log('📄 Creating invoices...');
  const invoices = [];
  for (const center of centers) {
    const hasAccounting = ['PRO', 'ENTERPRISE'].includes(center.subscriptionPlan);
    if (!hasAccounting) continue;

    const invoiceCount = faker.number.int({ min: 15, max: 30 });

    for (let i = 0; i < invoiceCount; i++) {
      const invoiceDate = faker.date.past({ years: 1 });
      const dueDate = new Date(invoiceDate);
      dueDate.setDate(dueDate.getDate() + faker.number.int({ min: 15, max: 45 }));

      const subtotal = randomDecimal(1000, 10000);
      const taxAmount = subtotal * 0.15; // 15% tax
      const totalAmount = subtotal + taxAmount;
      const paidAmount = Math.random() > 0.3 ? totalAmount : randomDecimal(0, totalAmount);

      const invoice = await prisma.invoice.create({
        data: {
          invoiceNumber: `INV-${center.branchCode}-${String(i + 1).padStart(4, '0')}`,
          clientName: faker.company.name(),
          clientEmail: faker.internet.email(),
          clientPhone: faker.phone.number(),
          clientAddress: faker.location.streetAddress({ useFullAddress: true }),
          invoiceDate,
          dueDate,
          subtotal,
          taxAmount,
          totalAmount,
          paidAmount,
          balanceAmount: totalAmount - paidAmount,
          status: paidAmount >= totalAmount ? 'PAID' : paidAmount > 0 ? 'PARTIALLY_PAID' : 'SENT',
          centerId: center.id,
        },
      });
      invoices.push(invoice);

      // Create invoice items
      const itemCount = faker.number.int({ min: 1, max: 5 });
      for (let j = 0; j < itemCount; j++) {
        await prisma.invoiceItem.create({
          data: {
            serviceName: faker.helpers.arrayElement([
              'Physiotherapy Session',
              'Consultation',
              'Exercise Program',
              'Manual Therapy',
              'Electrotherapy',
              'Rehabilitation Program',
            ]),
            description: faker.lorem.sentence(),
            quantity: faker.number.int({ min: 1, max: 10 }),
            unitPrice: randomDecimal(500, 2000),
            totalPrice: randomDecimal(500, 5000),
            invoiceId: invoice.id,
          },
        });
      }
    }
  }

  // Create Notifications
  console.log('🔔 Creating notifications...');
  const notifications = [];
  const allUsers = [...centerOwners, ...therapists.map(t => ({ id: t.userId }))];

  for (const user of allUsers) {
    if (!user.id) continue;

    const notificationCount = faker.number.int({ min: 3, max: 10 });

    for (let i = 0; i < notificationCount; i++) {
      const notification = await prisma.notification.create({
        data: {
          title: faker.helpers.arrayElement([
            'Appointment Reminder',
            'Session Completed',
            'Payment Received',
            'New Patient Registered',
            'System Update',
            'Subscription Expiring Soon',
          ]),
          body: faker.lorem.sentence(),
          type: faker.helpers.arrayElement(['REMINDER', 'APPOINTMENT', 'PAYMENT', 'SYSTEM']),
          isRead: faker.datatype.boolean(0.6), // 60% read
          scheduledFor: faker.date.recent({ days: 7 }),
          sentAt: faker.datatype.boolean(0.8) ? faker.date.recent({ days: 7 }) : null,
          userId: user.id,
        },
      });
      notifications.push(notification);
    }
  }

  console.log('✅ Database seeding completed!');
  console.log(`Created:
    - 1 super admin
    - ${centerOwners.length} center owners
    - ${centers.length} centers (including branches)
    - ${therapists.length} therapists
    - ${patients.length} patients
    - ${sessions.length} sessions
    - ${employees.length} employees
    - ${attendanceRecords.length} attendance records
    - ${expenses.length} expenses
    - ${invoices.length} invoices
    - ${notifications.length} notifications
  `);
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
