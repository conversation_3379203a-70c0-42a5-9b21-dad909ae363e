#!/usr/bin/env node

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { logger } from '../lib/logger.js';

/**
 * Development setup script
 * Sets up the database, runs migrations, and seeds data
 */

async function setupDevelopment() {
  try {
    console.log('🚀 Setting up development environment...\n');

    // Check if .env file exists
    if (!existsSync('.env')) {
      console.log('❌ .env file not found!');
      console.log('Please copy .env.example to .env and configure your environment variables.');
      process.exit(1);
    }

    // Install dependencies
    console.log('📦 Installing dependencies...');
    execSync('bun install', { stdio: 'inherit' });

    // Generate Prisma client
    console.log('\n🔧 Generating Prisma client...');
    execSync('bunx prisma generate', { stdio: 'inherit' });

    // Push database schema
    console.log('\n📊 Pushing database schema...');
    execSync('bunx prisma db push --force-reset', { stdio: 'inherit' });

    // Seed database
    console.log('\n🌱 Seeding database...');
    execSync('bun run src/scripts/seed.js', { stdio: 'inherit' });

    console.log('\n✅ Development environment setup complete!');
    console.log('\nYou can now start the development server with:');
    console.log('  bun run dev');
    console.log('\nAPI will be available at: http://localhost:3000');
    console.log('API Documentation: http://localhost:3000/docs');
    console.log('\nTest accounts created:');
    console.log('  - Super Admin: <EMAIL>');
    console.log('  - Center Owners: Various test accounts');
    console.log('  - Therapists: Linked to centers');
    console.log('  - Patients: Sample patient data');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run setup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupDevelopment();
}

export { setupDevelopment };
