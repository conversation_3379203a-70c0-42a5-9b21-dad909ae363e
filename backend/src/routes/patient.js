import { prisma } from '../config/database.js';
import { checkSubscriptionStatus, checkEntityLimit, requireFeature } from '../middleware/subscription.js';

/**
 * Patient management routes
 */
export default async function patientRoutes(fastify, options) {

  // Get all patients for a center
  fastify.get('/', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'THERAPIST']),
      checkSubscriptionStatus
    ],
    schema: {
      description: 'Get all patients for the center',
      tags: ['Patients'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          search: { type: 'string' },
          age: { type: 'integer' },
          isActive: { type: 'boolean' },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          offset: { type: 'integer', minimum: 0, default: 0 },
          sortBy: { type: 'string', enum: ['name', 'age', 'createdAt'], default: 'createdAt' },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            patients: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  age: { type: 'integer' },
                  phone: { type: ['string', 'null'] },
                  email: { type: ['string', 'null'] },
                  address: { type: ['string', 'null'] },
                  medicalHistory: { type: ['string', 'null'] },
                  profilePhoto: { type: ['string', 'null'] },
                  isActive: { type: 'boolean' },
                  createdAt: { type: 'string', format: 'date-time' },
                  updatedAt: { type: 'string', format: 'date-time' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                total: { type: 'integer' },
                limit: { type: 'integer' },
                offset: { type: 'integer' },
                hasMore: { type: 'boolean' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const {
      search,
      age,
      isActive,
      limit = 20,
      offset = 0,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = request.query;

    const center = request.center;

    // Build where clause
    const where = {
      centerId: center.id,
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { address: { contains: search, mode: 'insensitive' } }
        ]
      }),
      ...(age !== undefined && { age }),
      ...(isActive !== undefined && { isActive })
    };

    // Get patients with pagination
    const [patients, total] = await Promise.all([
      prisma.patient.findMany({
        where,
        include: {
          user: {
            select: {
              email: true,
              displayName: true
            }
          },
          _count: {
            select: {
              sessions: true
            }
          }
        },
        orderBy: { [sortBy]: sortOrder },
        take: limit,
        skip: offset
      }),
      prisma.patient.count({ where })
    ]);

    return {
      patients: patients.map(patient => ({
        ...patient,
        email: patient.user?.email || patient.email,
        sessionCount: patient._count.sessions
      })),
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    };
  });

  // Create patient
  fastify.post('/', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'THERAPIST']),
      checkSubscriptionStatus,
      checkEntityLimit('patients')
    ],
    schema: {
      description: 'Create new patient',
      tags: ['Patients'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['name', 'age'],
        properties: {
          name: { type: 'string', minLength: 2, maxLength: 100 },
          age: { type: 'integer', minimum: 0, maximum: 150 },
          phone: { type: 'string', pattern: '^[+]?[0-9\\s\\-\\(\\)]{10,20}$' },
          email: { type: 'string', format: 'email' },
          address: { type: 'string', maxLength: 500 },
          medicalHistory: { type: 'string', maxLength: 2000 },
          profilePhoto: { type: 'string', format: 'uri' },
          emergencyContact: { type: 'string', maxLength: 200 },
          bloodGroup: { type: 'string', enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'] },
          allergies: { type: 'string', maxLength: 500 },
          currentMedications: { type: 'string', maxLength: 500 },
          isActive: { type: 'boolean', default: true }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            message: { type: 'string' },
            patient: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                age: { type: 'integer' },
                phone: { type: ['string', 'null'] },
                email: { type: ['string', 'null'] },
                address: { type: ['string', 'null'] },
                medicalHistory: { type: ['string', 'null'] },
                profilePhoto: { type: ['string', 'null'] },
                isActive: { type: 'boolean' },
                createdAt: { type: 'string', format: 'date-time' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const patientData = request.body;
    const center = request.center;
    const user = request.user;

    // Check if patient with same phone/email already exists in this center
    if (patientData.phone || patientData.email) {
      const existingPatient = await prisma.patient.findFirst({
        where: {
          centerId: center.id,
          OR: [
            ...(patientData.phone ? [{ phone: patientData.phone }] : []),
            ...(patientData.email ? [{ email: patientData.email }] : [])
          ]
        }
      });

      if (existingPatient) {
        return reply.status(409).send({
          error: 'Patient with this phone number or email already exists in your center'
        });
      }
    }

    // Create patient
    const patient = await prisma.patient.create({
      data: {
        ...patientData,
        centerId: center.id,
        createdBy: user.id
      }
    });

    reply.status(201);
    return {
      message: 'Patient created successfully',
      patient
    };
  });

  // Get patient by ID
  fastify.get('/:patientId', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'THERAPIST', 'PATIENT']),
      checkSubscriptionStatus
    ],
    schema: {
      description: 'Get patient by ID',
      tags: ['Patients'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['patientId'],
        properties: {
          patientId: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    const { patientId } = request.params;
    const user = request.user;
    const center = request.center;

    // Build where clause based on user role
    let where = { id: patientId };

    if (user.role === 'PATIENT') {
      // Patients can only view their own records
      where.userId = user.id;
    } else {
      // Center owners and therapists can view patients in their center
      where.centerId = center.id;
    }

    const patient = await prisma.patient.findFirst({
      where,
      include: {
        user: {
          select: {
            email: true,
            displayName: true
          }
        },
        center: {
          select: {
            id: true,
            name: true,
            address: true
          }
        },
        sessions: {
          include: {
            therapist: {
              include: {
                user: {
                  select: {
                    displayName: true
                  }
                }
              }
            }
          },
          orderBy: { scheduledAt: 'desc' },
          take: 10 // Last 10 sessions
        },
        _count: {
          select: {
            sessions: true
          }
        }
      }
    });

    if (!patient) {
      return reply.status(404).send({ error: 'Patient not found' });
    }

    return {
      patient: {
        ...patient,
        email: patient.user?.email || patient.email,
        sessionCount: patient._count.sessions
      }
    };
  });

  // Update patient
  fastify.put('/:patientId', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'THERAPIST']),
      checkSubscriptionStatus
    ],
    schema: {
      description: 'Update patient information',
      tags: ['Patients'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['patientId'],
        properties: {
          patientId: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        properties: {
          name: { type: 'string', minLength: 2, maxLength: 100 },
          age: { type: 'integer', minimum: 0, maximum: 150 },
          phone: { type: 'string' },
          email: { type: 'string', format: 'email' },
          address: { type: 'string', maxLength: 500 },
          medicalHistory: { type: 'string', maxLength: 2000 },
          profilePhoto: { type: 'string' },
          emergencyContact: { type: 'string', maxLength: 200 },
          bloodGroup: { type: 'string', enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'] },
          allergies: { type: 'string', maxLength: 500 },
          currentMedications: { type: 'string', maxLength: 500 },
          isActive: { type: 'boolean' }
        }
      }
    }
  }, async (request, reply) => {
    const { patientId } = request.params;
    const updateData = request.body;
    const center = request.center;
    const user = request.user;

    // Check if patient exists and belongs to the center
    const existingPatient = await prisma.patient.findFirst({
      where: {
        id: patientId,
        centerId: center.id
      }
    });

    if (!existingPatient) {
      return reply.status(404).send({ error: 'Patient not found' });
    }

    // Update patient
    const updatedPatient = await prisma.patient.update({
      where: { id: patientId },
      data: {
        ...updateData,
        updatedBy: user.id,
        updatedAt: new Date()
      }
    });

    return {
      message: 'Patient updated successfully',
      patient: updatedPatient
    };
  });

  // Delete patient
  fastify.delete('/:patientId', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER']),
      checkSubscriptionStatus
    ],
    schema: {
      description: 'Delete patient (soft delete)',
      tags: ['Patients'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['patientId'],
        properties: {
          patientId: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    const { patientId } = request.params;
    const center = request.center;

    // Check if patient exists and belongs to the center
    const patient = await prisma.patient.findFirst({
      where: {
        id: patientId,
        centerId: center.id
      },
      include: {
        _count: {
          select: {
            sessions: true
          }
        }
      }
    });

    if (!patient) {
      return reply.status(404).send({ error: 'Patient not found' });
    }

    // Check if patient has sessions
    if (patient._count.sessions > 0) {
      // Soft delete - just mark as inactive
      await prisma.patient.update({
        where: { id: patientId },
        data: {
          isActive: false,
          updatedAt: new Date()
        }
      });

      return {
        message: 'Patient deactivated successfully (has existing sessions)'
      };
    } else {
      // Hard delete if no sessions
      await prisma.patient.delete({
        where: { id: patientId }
      });

      return {
        message: 'Patient deleted successfully'
      };
    }
  });
}
