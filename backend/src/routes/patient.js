/**
 * Patient management routes
 * TODO: Implement patient CRUD operations
 */
export default async function patientRoutes(fastify, options) {
  
  // Get all patients for a center
  fastify.get('/', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'THERAPIST'])],
    schema: {
      description: 'Get all patients',
      tags: ['Patients'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    return { message: 'Patient routes - TODO: Implement' };
  });

  // Create patient
  fastify.post('/', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'THERAPIST'])],
    schema: {
      description: 'Create new patient',
      tags: ['Patients'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    return { message: 'Create patient - TODO: Implement' };
  });

  // Get patient by ID
  fastify.get('/:patientId', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'THERAPIST', 'PATIENT'])],
    schema: {
      description: 'Get patient by ID',
      tags: ['Patients'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    return { message: 'Get patient - TODO: Implement' };
  });

  // Update patient
  fastify.put('/:patientId', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'THERAPIST'])],
    schema: {
      description: 'Update patient',
      tags: ['Patients'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    return { message: 'Update patient - TODO: Implement' };
  });

  // Delete patient
  fastify.delete('/:patientId', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER'])],
    schema: {
      description: 'Delete patient',
      tags: ['Patients'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    return { message: 'Delete patient - TODO: Implement' };
  });
}
