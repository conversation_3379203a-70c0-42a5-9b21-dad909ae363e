import { prisma } from '../config/database.js';
import { getAllPlans, getPlanLimits } from '../middleware/subscription.js';

/**
 * Subscription management routes
 */
export default async function subscriptionRoutes(fastify, options) {

  // Get subscription plans
  fastify.get('/plans', {
    schema: {
      description: 'Get available subscription plans',
      tags: ['Subscriptions'],
      response: {
        200: {
          type: 'object',
          properties: {
            plans: {
              type: 'object',
              additionalProperties: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  price: { type: 'number' },
                  currency: { type: 'string' },
                  duration: { type: ['number', 'null'] },
                  maxPatients: { type: 'number' },
                  maxTherapists: { type: 'number' },
                  maxEmployees: { type: 'number' },
                  maxBranches: { type: 'number' },
                  maxSessions: { type: 'number' },
                  maxStorage: { type: 'number' },
                  features: { type: 'object' }
                }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const plans = getAllPlans();
    return { plans };
  });

  // Get current subscription
  fastify.get('/current', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'THERAPIST'])],
    schema: {
      description: 'Get current subscription details',
      tags: ['Subscriptions'],
      security: [{ Bearer: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            subscription: {
              type: 'object',
              properties: {
                plan: { type: 'string' },
                planName: { type: 'string' },
                subscriptionStart: { type: 'string', format: 'date-time' },
                subscriptionEnd: { type: ['string', 'null'], format: 'date-time' },
                isActive: { type: 'boolean' },
                daysRemaining: { type: ['number', 'null'] },
                features: { type: 'object' },
                limits: { type: 'object' },
                usage: { type: 'object' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const user = request.user;

    // Get user's center
    const center = await prisma.center.findFirst({
      where: {
        OR: [
          { ownerId: user.id },
          { therapists: { some: { userId: user.id } } }
        ]
      },
      include: {
        _count: {
          select: {
            patients: true,
            therapists: true,
            employees: true,
            branches: true,
            sessions: true,
          }
        }
      }
    });

    if (!center) {
      return reply.status(404).send({ error: 'No center found for user' });
    }

    const planLimits = getPlanLimits(center.subscriptionPlan);
    const now = new Date();
    const isActive = !center.subscriptionEnd || now <= center.subscriptionEnd;

    let daysRemaining = null;
    if (center.subscriptionEnd) {
      daysRemaining = Math.ceil((center.subscriptionEnd - now) / (1000 * 60 * 60 * 24));
    }

    return {
      subscription: {
        plan: center.subscriptionPlan,
        planName: planLimits.name,
        subscriptionStart: center.subscriptionStart,
        subscriptionEnd: center.subscriptionEnd,
        isActive,
        daysRemaining,
        features: planLimits.features,
        limits: {
          patients: planLimits.maxPatients,
          therapists: planLimits.maxTherapists,
          employees: planLimits.maxEmployees,
          branches: planLimits.maxBranches,
          sessions: planLimits.maxSessions,
          storage: planLimits.maxStorage,
        },
        usage: {
          patients: center._count.patients,
          therapists: center._count.therapists,
          employees: center._count.employees,
          branches: center._count.branches,
          sessions: center._count.sessions,
        }
      }
    };
  });

  // Upgrade subscription
  fastify.post('/upgrade', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER'])],
    schema: {
      description: 'Upgrade subscription plan',
      tags: ['Subscriptions'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['plan'],
        properties: {
          plan: {
            type: 'string',
            enum: ['TRIAL', 'BASIC', 'PRO', 'ENTERPRISE']
          },
          paymentMethod: { type: 'string' },
          paymentReference: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            message: { type: 'string' },
            subscription: { type: 'object' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { plan, paymentMethod, paymentReference } = request.body;
    const user = request.user;

    // Get user's center
    const center = await prisma.center.findFirst({
      where: { ownerId: user.id }
    });

    if (!center) {
      return reply.status(404).send({ error: 'No center found for user' });
    }

    // Validate plan upgrade
    const currentPlan = center.subscriptionPlan;
    const planHierarchy = ['TRIAL', 'BASIC', 'PRO', 'ENTERPRISE'];
    const currentIndex = planHierarchy.indexOf(currentPlan);
    const newIndex = planHierarchy.indexOf(plan);

    if (newIndex <= currentIndex && currentPlan !== 'TRIAL') {
      return reply.status(400).send({
        error: 'Cannot downgrade subscription plan',
        currentPlan,
        requestedPlan: plan
      });
    }

    const planLimits = getPlanLimits(plan);
    const now = new Date();

    // Calculate subscription end date
    let subscriptionEnd = null;
    if (planLimits.duration) {
      subscriptionEnd = new Date();
      subscriptionEnd.setDate(subscriptionEnd.getDate() + planLimits.duration);
    }

    // Update center subscription
    const updatedCenter = await prisma.center.update({
      where: { id: center.id },
      data: {
        subscriptionPlan: plan,
        subscriptionStart: now,
        subscriptionEnd,
        updatedAt: now
      }
    });

    // Log subscription change
    await prisma.subscriptionLog.create({
      data: {
        centerId: center.id,
        fromPlan: currentPlan,
        toPlan: plan,
        amount: planLimits.price,
        currency: planLimits.currency,
        paymentMethod: paymentMethod || 'MANUAL',
        paymentReference: paymentReference || null,
        createdAt: now
      }
    });

    return {
      message: `Subscription upgraded to ${planLimits.name} plan`,
      subscription: {
        plan: updatedCenter.subscriptionPlan,
        planName: planLimits.name,
        subscriptionStart: updatedCenter.subscriptionStart,
        subscriptionEnd: updatedCenter.subscriptionEnd,
        features: planLimits.features,
        limits: {
          patients: planLimits.maxPatients,
          therapists: planLimits.maxTherapists,
          employees: planLimits.maxEmployees,
          branches: planLimits.maxBranches,
          sessions: planLimits.maxSessions,
          storage: planLimits.maxStorage,
        }
      }
    };
  });

  // Get subscription history
  fastify.get('/history', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER'])],
    schema: {
      description: 'Get subscription change history',
      tags: ['Subscriptions'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          offset: { type: 'integer', minimum: 0, default: 0 }
        }
      }
    }
  }, async (request, reply) => {
    const { limit = 20, offset = 0 } = request.query;
    const user = request.user;

    // Get user's center
    const center = await prisma.center.findFirst({
      where: { ownerId: user.id }
    });

    if (!center) {
      return reply.status(404).send({ error: 'No center found for user' });
    }

    const history = await prisma.subscriptionLog.findMany({
      where: { centerId: center.id },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset
    });

    const total = await prisma.subscriptionLog.count({
      where: { centerId: center.id }
    });

    return {
      history,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    };
  });

  // Cancel subscription (downgrade to trial)
  fastify.post('/cancel', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER'])],
    schema: {
      description: 'Cancel subscription (downgrade to trial)',
      tags: ['Subscriptions'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          reason: { type: 'string' },
          feedback: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    const { reason, feedback } = request.body;
    const user = request.user;

    // Get user's center
    const center = await prisma.center.findFirst({
      where: { ownerId: user.id }
    });

    if (!center) {
      return reply.status(404).send({ error: 'No center found for user' });
    }

    if (center.subscriptionPlan === 'TRIAL') {
      return reply.status(400).send({ error: 'Already on trial plan' });
    }

    const currentPlan = center.subscriptionPlan;
    const trialPlan = getPlanLimits('TRIAL');
    const now = new Date();

    // Set trial end date (30 days from now)
    const trialEnd = new Date();
    trialEnd.setDate(trialEnd.getDate() + 30);

    // Update center to trial
    const updatedCenter = await prisma.center.update({
      where: { id: center.id },
      data: {
        subscriptionPlan: 'TRIAL',
        subscriptionStart: now,
        subscriptionEnd: trialEnd,
        updatedAt: now
      }
    });

    // Log cancellation
    await prisma.subscriptionLog.create({
      data: {
        centerId: center.id,
        fromPlan: currentPlan,
        toPlan: 'TRIAL',
        amount: 0,
        currency: 'BDT',
        paymentMethod: 'CANCELLATION',
        paymentReference: null,
        notes: reason ? `Reason: ${reason}. Feedback: ${feedback || 'None'}` : null,
        createdAt: now
      }
    });

    return {
      message: 'Subscription cancelled. Downgraded to trial plan.',
      subscription: {
        plan: 'TRIAL',
        planName: trialPlan.name,
        subscriptionStart: updatedCenter.subscriptionStart,
        subscriptionEnd: updatedCenter.subscriptionEnd,
        features: trialPlan.features,
        limits: {
          patients: trialPlan.maxPatients,
          therapists: trialPlan.maxTherapists,
          employees: trialPlan.maxEmployees,
          branches: trialPlan.maxBranches,
          sessions: trialPlan.maxSessions,
          storage: trialPlan.maxStorage,
        }
      }
    };
  });
}
