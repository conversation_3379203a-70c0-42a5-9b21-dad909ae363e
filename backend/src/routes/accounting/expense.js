import { prisma } from '../../lib/prisma.js';
import { logger } from '../../lib/logger.js';
import { requireFeature } from '../../middleware/subscriptionMiddleware.js';

/**
 * Expense management routes (Accounting module)
 */
export default async function expenseRoutes(fastify, options) {
  
  // Get all expenses for a center
  fastify.get('/', {
    preHandler: [
      fastify.authenticate, 
      fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN']),
      requireFeature('accountingModule')
    ],
    schema: {
      description: 'Get all expenses',
      tags: ['Accounting - Expenses'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          centerId: { type: 'string' },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          search: { type: 'string' },
          category: { 
            type: 'string', 
            enum: ['OFFICE_SUPPLIES', 'EQUIPMENT', 'UTILITIES', 'RENT', 'MARKETING', 'TRAVEL', 'MEALS', 'PROFESSIONAL_SERVICES', 'INSURANCE', 'MAINTENANCE', 'OTHER']
          },
          status: { type: 'string', enum: ['PENDING', 'APPROVED', 'REJECTED', 'PAID'] },
          startDate: { type: 'string', format: 'date' },
          endDate: { type: 'string', format: 'date' },
          minAmount: { type: 'number', minimum: 0 },
          maxAmount: { type: 'number', minimum: 0 }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { 
        centerId, 
        page = 1, 
        limit = 20, 
        search, 
        category, 
        status, 
        startDate, 
        endDate,
        minAmount,
        maxAmount
      } = request.query;
      const skip = (page - 1) * limit;

      // Determine center ID
      let targetCenterId = centerId;
      if (!targetCenterId && request.user.role === 'CENTER_OWNER') {
        targetCenterId = request.user.ownedCenters[0]?.id;
      }

      if (!targetCenterId) {
        reply.code(400);
        return {
          error: 'Bad Request',
          message: 'Center ID is required',
          statusCode: 400,
          timestamp: new Date().toISOString()
        };
      }

      const where = { centerId: targetCenterId };
      
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { vendor: { contains: search, mode: 'insensitive' } },
          { expenseNumber: { contains: search, mode: 'insensitive' } }
        ];
      }
      
      if (category) {
        where.category = category;
      }
      
      if (status) {
        where.status = status;
      }

      if (startDate || endDate) {
        where.expenseDate = {};
        if (startDate) {
          where.expenseDate.gte = new Date(startDate);
        }
        if (endDate) {
          where.expenseDate.lte = new Date(endDate);
        }
      }

      if (minAmount !== undefined || maxAmount !== undefined) {
        where.amount = {};
        if (minAmount !== undefined) {
          where.amount.gte = minAmount;
        }
        if (maxAmount !== undefined) {
          where.amount.lte = maxAmount;
        }
      }

      const [expenses, total, totalAmount] = await Promise.all([
        prisma.expense.findMany({
          where,
          include: {
            center: {
              select: { id: true, name: true }
            }
          },
          orderBy: { expenseDate: 'desc' },
          take: limit,
          skip
        }),
        prisma.expense.count({ where }),
        prisma.expense.aggregate({
          where,
          _sum: { amount: true }
        })
      ]);

      return {
        expenses,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        },
        summary: {
          totalAmount: totalAmount._sum.amount || 0,
          count: total
        }
      };

    } catch (error) {
      logger.error('Get expenses error:', error);
      throw error;
    }
  });

  // Create expense
  fastify.post('/', {
    preHandler: [
      fastify.authenticate, 
      fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN']),
      requireFeature('accountingModule')
    ],
    schema: {
      description: 'Create new expense',
      tags: ['Accounting - Expenses'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['title', 'category', 'amount', 'expenseDate', 'centerId'],
        properties: {
          title: { type: 'string', minLength: 2, maxLength: 200 },
          description: { type: 'string' },
          category: { 
            type: 'string', 
            enum: ['OFFICE_SUPPLIES', 'EQUIPMENT', 'UTILITIES', 'RENT', 'MARKETING', 'TRAVEL', 'MEALS', 'PROFESSIONAL_SERVICES', 'INSURANCE', 'MAINTENANCE', 'OTHER']
          },
          amount: { type: 'number', minimum: 0 },
          currency: { type: 'string', default: 'BDT' },
          expenseDate: { type: 'string', format: 'date' },
          paymentMethod: { 
            type: 'string', 
            enum: ['CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'MOBILE_BANKING', 'CHECK', 'OTHER']
          },
          vendor: { type: 'string' },
          receipt: { type: 'string' },
          centerId: { type: 'string' },
          notes: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const expenseData = request.body;

      // Generate expense number
      const centerExpenseCount = await prisma.expense.count({
        where: { centerId: expenseData.centerId }
      });
      
      const center = await prisma.center.findUnique({
        where: { id: expenseData.centerId },
        select: { branchCode: true }
      });

      const expenseNumber = `EXP-${center.branchCode}-${String(centerExpenseCount + 1).padStart(4, '0')}`;

      const expense = await prisma.expense.create({
        data: {
          ...expenseData,
          expenseNumber,
          expenseDate: new Date(expenseData.expenseDate),
          status: 'PENDING'
        },
        include: {
          center: {
            select: { id: true, name: true }
          }
        }
      });

      logger.info(`Expense created: ${expense.title} (${expense.expenseNumber}) by user ${request.user.email}`);

      return {
        expense,
        message: 'Expense created successfully'
      };

    } catch (error) {
      logger.error('Create expense error:', error);
      throw error;
    }
  });

  // Get expense by ID
  fastify.get('/:expenseId', {
    preHandler: [
      fastify.authenticate, 
      fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN']),
      requireFeature('accountingModule')
    ],
    schema: {
      description: 'Get expense by ID',
      tags: ['Accounting - Expenses'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['expenseId'],
        properties: {
          expenseId: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { expenseId } = request.params;

      const expense = await prisma.expense.findUnique({
        where: { id: expenseId },
        include: {
          center: {
            select: { id: true, name: true }
          }
        }
      });

      if (!expense) {
        reply.code(404);
        return {
          error: 'Not Found',
          message: 'Expense not found',
          statusCode: 404,
          timestamp: new Date().toISOString()
        };
      }

      return { expense };

    } catch (error) {
      logger.error('Get expense error:', error);
      throw error;
    }
  });

  // Update expense
  fastify.put('/:expenseId', {
    preHandler: [
      fastify.authenticate, 
      fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN']),
      requireFeature('accountingModule')
    ],
    schema: {
      description: 'Update expense',
      tags: ['Accounting - Expenses'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['expenseId'],
        properties: {
          expenseId: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        properties: {
          title: { type: 'string', minLength: 2, maxLength: 200 },
          description: { type: 'string' },
          category: { 
            type: 'string', 
            enum: ['OFFICE_SUPPLIES', 'EQUIPMENT', 'UTILITIES', 'RENT', 'MARKETING', 'TRAVEL', 'MEALS', 'PROFESSIONAL_SERVICES', 'INSURANCE', 'MAINTENANCE', 'OTHER']
          },
          amount: { type: 'number', minimum: 0 },
          expenseDate: { type: 'string', format: 'date' },
          paymentMethod: { 
            type: 'string', 
            enum: ['CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'MOBILE_BANKING', 'CHECK', 'OTHER']
          },
          vendor: { type: 'string' },
          status: { type: 'string', enum: ['PENDING', 'APPROVED', 'REJECTED', 'PAID'] },
          notes: { type: 'string' },
          approvedBy: { type: 'string' },
          approvedAt: { type: 'string', format: 'date-time' },
          paidAt: { type: 'string', format: 'date-time' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { expenseId } = request.params;
      const updateData = request.body;

      // Process date fields
      if (updateData.expenseDate) {
        updateData.expenseDate = new Date(updateData.expenseDate);
      }
      if (updateData.approvedAt) {
        updateData.approvedAt = new Date(updateData.approvedAt);
      }
      if (updateData.paidAt) {
        updateData.paidAt = new Date(updateData.paidAt);
      }

      const updatedExpense = await prisma.expense.update({
        where: { id: expenseId },
        data: updateData,
        include: {
          center: {
            select: { id: true, name: true }
          }
        }
      });

      logger.info(`Expense updated: ${updatedExpense.title} (${updatedExpense.expenseNumber}) by user ${request.user.email}`);

      return {
        expense: updatedExpense,
        message: 'Expense updated successfully'
      };

    } catch (error) {
      logger.error('Update expense error:', error);
      throw error;
    }
  });

  // Delete expense
  fastify.delete('/:expenseId', {
    preHandler: [
      fastify.authenticate, 
      fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN']),
      requireFeature('accountingModule')
    ],
    schema: {
      description: 'Delete expense',
      tags: ['Accounting - Expenses'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['expenseId'],
        properties: {
          expenseId: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { expenseId } = request.params;

      const deletedExpense = await prisma.expense.delete({
        where: { id: expenseId }
      });

      logger.info(`Expense deleted: ${deletedExpense.title} (${deletedExpense.expenseNumber}) by user ${request.user.email}`);

      return {
        message: 'Expense deleted successfully'
      };

    } catch (error) {
      logger.error('Delete expense error:', error);
      throw error;
    }
  });
}
