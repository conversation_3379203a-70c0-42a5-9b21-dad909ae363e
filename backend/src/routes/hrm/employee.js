import { prisma } from '../../lib/prisma.js';
import { logger } from '../../lib/logger.js';
import { requireFeature, checkLimit, countFunctions } from '../../middleware/subscriptionMiddleware.js';

/**
 * Employee management routes (HRM module)
 */
export default async function employeeRoutes(fastify, options) {
  
  // Get all employees for a center
  fastify.get('/', {
    preHandler: [
      fastify.authenticate, 
      fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN']),
      requireFeature('hrmModule')
    ],
    schema: {
      description: 'Get all employees',
      tags: ['HRM - Employees'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          centerId: { type: 'string' },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          search: { type: 'string' },
          department: { type: 'string' },
          status: { type: 'string', enum: ['ACTIVE', 'INACTIVE', 'TERMINATED', 'ON_LEAVE'] },
          employmentType: { type: 'string', enum: ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERN'] }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { centerId, page = 1, limit = 20, search, department, status, employmentType } = request.query;
      const skip = (page - 1) * limit;

      // Determine center ID
      let targetCenterId = centerId;
      if (!targetCenterId && request.user.role === 'CENTER_OWNER') {
        targetCenterId = request.user.ownedCenters[0]?.id;
      }

      if (!targetCenterId) {
        reply.code(400);
        return {
          error: 'Bad Request',
          message: 'Center ID is required',
          statusCode: 400,
          timestamp: new Date().toISOString()
        };
      }

      const where = { centerId: targetCenterId };
      
      if (search) {
        where.OR = [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { employeeId: { contains: search, mode: 'insensitive' } },
          { position: { contains: search, mode: 'insensitive' } }
        ];
      }
      
      if (department) {
        where.department = department;
      }
      
      if (status) {
        where.status = status;
      }
      
      if (employmentType) {
        where.employmentType = employmentType;
      }

      const [employees, total] = await Promise.all([
        prisma.employee.findMany({
          where,
          include: {
            center: {
              select: { id: true, name: true }
            },
            user: {
              select: { id: true, displayName: true, email: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip
        }),
        prisma.employee.count({ where })
      ]);

      return {
        employees,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      };

    } catch (error) {
      logger.error('Get employees error:', error);
      throw error;
    }
  });

  // Create employee
  fastify.post('/', {
    preHandler: [
      fastify.authenticate, 
      fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN']),
      requireFeature('hrmModule'),
      checkLimit('maxEmployees', countFunctions.maxEmployees)
    ],
    schema: {
      description: 'Create new employee',
      tags: ['HRM - Employees'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['firstName', 'lastName', 'position', 'centerId'],
        properties: {
          firstName: { type: 'string', minLength: 2, maxLength: 50 },
          lastName: { type: 'string', minLength: 2, maxLength: 50 },
          email: { type: 'string', format: 'email' },
          phone: { type: 'string' },
          address: { type: 'string' },
          dateOfBirth: { type: 'string', format: 'date' },
          hireDate: { type: 'string', format: 'date' },
          position: { type: 'string', minLength: 2, maxLength: 100 },
          department: { type: 'string' },
          salary: { type: 'number', minimum: 0 },
          hourlyRate: { type: 'number', minimum: 0 },
          employmentType: { type: 'string', enum: ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERN'] },
          emergencyContact: { type: 'string' },
          emergencyPhone: { type: 'string' },
          centerId: { type: 'string' },
          notes: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const employeeData = request.body;

      // Generate employee ID
      const centerEmployeeCount = await prisma.employee.count({
        where: { centerId: employeeData.centerId }
      });
      
      const center = await prisma.center.findUnique({
        where: { id: employeeData.centerId },
        select: { branchCode: true }
      });

      const employeeId = `EMP-${center.branchCode}-${String(centerEmployeeCount + 1).padStart(3, '0')}`;

      const employee = await prisma.employee.create({
        data: {
          ...employeeData,
          employeeId,
          dateOfBirth: employeeData.dateOfBirth ? new Date(employeeData.dateOfBirth) : null,
          hireDate: employeeData.hireDate ? new Date(employeeData.hireDate) : new Date(),
          status: 'ACTIVE'
        },
        include: {
          center: {
            select: { id: true, name: true }
          }
        }
      });

      logger.info(`Employee created: ${employee.firstName} ${employee.lastName} by user ${request.user.email}`);

      return {
        employee,
        message: 'Employee created successfully'
      };

    } catch (error) {
      logger.error('Create employee error:', error);
      throw error;
    }
  });

  // Get employee by ID
  fastify.get('/:employeeId', {
    preHandler: [
      fastify.authenticate, 
      fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN']),
      requireFeature('hrmModule')
    ],
    schema: {
      description: 'Get employee by ID',
      tags: ['HRM - Employees'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['employeeId'],
        properties: {
          employeeId: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { employeeId } = request.params;

      const employee = await prisma.employee.findUnique({
        where: { id: employeeId },
        include: {
          center: {
            select: { id: true, name: true }
          },
          user: {
            select: { id: true, displayName: true, email: true }
          },
          attendanceRecords: {
            take: 10,
            orderBy: { date: 'desc' }
          },
          leaveRequests: {
            take: 5,
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      if (!employee) {
        reply.code(404);
        return {
          error: 'Not Found',
          message: 'Employee not found',
          statusCode: 404,
          timestamp: new Date().toISOString()
        };
      }

      return { employee };

    } catch (error) {
      logger.error('Get employee error:', error);
      throw error;
    }
  });

  // Update employee
  fastify.put('/:employeeId', {
    preHandler: [
      fastify.authenticate, 
      fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN']),
      requireFeature('hrmModule')
    ],
    schema: {
      description: 'Update employee',
      tags: ['HRM - Employees'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['employeeId'],
        properties: {
          employeeId: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        properties: {
          firstName: { type: 'string', minLength: 2, maxLength: 50 },
          lastName: { type: 'string', minLength: 2, maxLength: 50 },
          email: { type: 'string', format: 'email' },
          phone: { type: 'string' },
          address: { type: 'string' },
          position: { type: 'string' },
          department: { type: 'string' },
          salary: { type: 'number', minimum: 0 },
          hourlyRate: { type: 'number', minimum: 0 },
          employmentType: { type: 'string', enum: ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERN'] },
          status: { type: 'string', enum: ['ACTIVE', 'INACTIVE', 'TERMINATED', 'ON_LEAVE'] },
          emergencyContact: { type: 'string' },
          emergencyPhone: { type: 'string' },
          notes: { type: 'string' },
          terminationDate: { type: 'string', format: 'date' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { employeeId } = request.params;
      const updateData = request.body;

      // Process date fields
      if (updateData.terminationDate) {
        updateData.terminationDate = new Date(updateData.terminationDate);
      }

      const updatedEmployee = await prisma.employee.update({
        where: { id: employeeId },
        data: updateData,
        include: {
          center: {
            select: { id: true, name: true }
          }
        }
      });

      logger.info(`Employee updated: ${updatedEmployee.firstName} ${updatedEmployee.lastName} by user ${request.user.email}`);

      return {
        employee: updatedEmployee,
        message: 'Employee updated successfully'
      };

    } catch (error) {
      logger.error('Update employee error:', error);
      throw error;
    }
  });

  // Delete employee
  fastify.delete('/:employeeId', {
    preHandler: [
      fastify.authenticate, 
      fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN']),
      requireFeature('hrmModule')
    ],
    schema: {
      description: 'Delete employee',
      tags: ['HRM - Employees'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['employeeId'],
        properties: {
          employeeId: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { employeeId } = request.params;

      // Soft delete by marking as terminated
      const updatedEmployee = await prisma.employee.update({
        where: { id: employeeId },
        data: { 
          status: 'TERMINATED',
          terminationDate: new Date()
        }
      });

      logger.info(`Employee terminated: ${updatedEmployee.firstName} ${updatedEmployee.lastName} by user ${request.user.email}`);

      return {
        message: 'Employee terminated successfully'
      };

    } catch (error) {
      logger.error('Delete employee error:', error);
      throw error;
    }
  });
}
