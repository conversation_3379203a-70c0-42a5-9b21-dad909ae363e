import { AuthService } from '../services/authService.js';
import { logger } from '../lib/logger.js';

/**
 * Authentication routes
 */
export default async function authRoutes(fastify, options) {
  
  // Login/Register with Firebase
  fastify.post('/login', {
    schema: {
      description: 'Login or register user with Firebase token',
      tags: ['Authentication'],
      body: {
        type: 'object',
        required: ['idToken'],
        properties: {
          idToken: { type: 'string', description: 'Firebase ID token' },
          fcmToken: { type: 'string', description: 'FCM token for push notifications' },
          role: { 
            type: 'string', 
            enum: ['CENTER_OWNER', 'THERAPIST', 'PATIENT'],
            description: 'User role (only for new users)'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                firebaseUid: { type: 'string' },
                email: { type: 'string' },
                displayName: { type: 'string' },
                photoURL: { type: 'string' },
                role: { type: 'string' },
                isActive: { type: 'boolean' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' }
              }
            },
            isNewUser: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { idToken, fcmToken, role = 'CENTER_OWNER' } = request.body;

      // Use AuthService for login/register
      const { user, isNewUser } = await AuthService.loginOrRegister(idToken, fcmToken, role);

      // Check if user is active
      if (!user.isActive) {
        reply.code(403);
        return {
          error: 'Account Deactivated',
          message: 'Your account has been deactivated. Please contact support.',
          statusCode: 403,
          timestamp: new Date().toISOString()
        };
      }

      return {
        user: {
          id: user.id,
          firebaseUid: user.firebaseUid,
          email: user.email,
          displayName: user.displayName,
          photoURL: user.photoURL,
          role: user.role,
          isActive: user.isActive,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          ownedCenters: user.ownedCenters,
          therapistProfile: user.therapistProfile,
          patientProfile: user.patientProfile
        },
        isNewUser,
        message: isNewUser ? 'User registered successfully' : 'User logged in successfully'
      };

    } catch (error) {
      logger.error('Login failed:', error);
      throw error;
    }
  });

  // Get current user profile
  fastify.get('/profile', {
    preHandler: [fastify.authenticate],
    schema: {
      description: 'Get current user profile',
      tags: ['Authentication'],
      security: [{ Bearer: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                firebaseUid: { type: 'string' },
                email: { type: 'string' },
                displayName: { type: 'string' },
                photoURL: { type: 'string' },
                role: { type: 'string' },
                isActive: { type: 'boolean' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' },
                ownedCenters: { type: 'array' },
                therapistProfile: { type: 'object' },
                patientProfile: { type: 'object' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    return {
      user: request.user
    };
  });

  // Update user profile
  fastify.put('/profile', {
    preHandler: [fastify.authenticate],
    schema: {
      description: 'Update user profile',
      tags: ['Authentication'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          displayName: { type: 'string' },
          fcmToken: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            user: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { displayName, fcmToken } = request.body;

      // Use AuthService for profile update
      const updatedUser = await AuthService.updateProfile(request.user.id, {
        displayName,
        fcmToken
      });

      return {
        user: updatedUser,
        message: 'Profile updated successfully'
      };

    } catch (error) {
      logger.error('Profile update failed:', error);
      throw error;
    }
  });

  // Logout (clear FCM token)
  fastify.post('/logout', {
    preHandler: [fastify.authenticate],
    schema: {
      description: 'Logout user (clear FCM token)',
      tags: ['Authentication'],
      security: [{ Bearer: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      // Use AuthService for logout
      await AuthService.logout(request.user.id);

      return {
        message: 'Logged out successfully'
      };

    } catch (error) {
      logger.error('Logout failed:', error);
      throw error;
    }
  });
}
