import { prisma } from '../lib/prisma.js';
import { logger } from '../lib/logger.js';
import bcrypt from 'bcryptjs';

/**
 * Admin routes for managing centers and system
 */
export default async function adminRoutes(fastify, options) {
  
  // Admin middleware to verify admin token
  const verifyAdmin = async (request, reply) => {
    try {
      const token = request.headers.authorization?.replace('Bearer ', '');
      if (!token) {
        return reply.code(401).send({
          success: false,
          message: 'Access token required'
        });
      }

      const decoded = fastify.jwt.verify(token);
      if (decoded.role !== 'admin' || decoded.type !== 'admin') {
        return reply.code(403).send({
          success: false,
          message: 'Admin access required'
        });
      }

      request.admin = decoded;
    } catch (error) {
      return reply.code(401).send({
        success: false,
        message: 'Invalid token'
      });
    }
  };

  // Get all centers
  fastify.get('/centers', {
    preHandler: [verifyAdmin],
    schema: {
      description: 'Get all centers (Admin only)',
      tags: ['Admin'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  address: { type: 'string' },
                  phone: { type: 'string' },
                  email: { type: 'string' },
                  subscriptionPlan: { type: 'string' },
                  isActive: { type: 'boolean' },
                  createdAt: { type: 'string' },
                  owner: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      email: { type: 'string' },
                      displayName: { type: 'string' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const centers = await prisma.center.findMany({
        include: {
          owner: {
            select: {
              id: true,
              email: true,
              displayName: true
            }
          },
          _count: {
            select: {
              patients: true,
              therapists: true,
              sessions: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return reply.send({
        success: true,
        data: centers
      });
    } catch (error) {
      logger.error('Get centers error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Create center (Admin only)
  fastify.post('/centers', {
    preHandler: [verifyAdmin],
    schema: {
      description: 'Create a new center (Admin only)',
      tags: ['Admin'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['name', 'address', 'ownerEmail', 'ownerName'],
        properties: {
          name: { type: 'string', minLength: 2 },
          address: { type: 'string', minLength: 5 },
          phone: { type: 'string' },
          email: { type: 'string', format: 'email' },
          licenseNumber: { type: 'string' },
          licenseExpiry: { type: 'string', format: 'date' },
          subscriptionPlan: { 
            type: 'string', 
            enum: ['TRIAL', 'BASIC', 'PRO'],
            default: 'TRIAL'
          },
          ownerEmail: { type: 'string', format: 'email' },
          ownerName: { type: 'string', minLength: 2 },
          ownerFirebaseUid: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const {
        name,
        address,
        phone,
        email,
        licenseNumber,
        licenseExpiry,
        subscriptionPlan = 'TRIAL',
        ownerEmail,
        ownerName,
        ownerFirebaseUid
      } = request.body;

      // Check if center with same name already exists
      const existingCenter = await prisma.center.findFirst({
        where: { name }
      });

      if (existingCenter) {
        return reply.code(400).send({
          success: false,
          message: 'Center with this name already exists'
        });
      }

      // Create or find the owner user
      let owner = await prisma.user.findUnique({
        where: { email: ownerEmail }
      });

      if (!owner && ownerFirebaseUid) {
        // Create new user if doesn't exist
        owner = await prisma.user.create({
          data: {
            firebaseUid: ownerFirebaseUid,
            email: ownerEmail,
            displayName: ownerName,
            role: 'CENTER_OWNER',
            isActive: true
          }
        });
      } else if (!owner) {
        return reply.code(400).send({
          success: false,
          message: 'Owner user not found. Please provide Firebase UID to create new user.'
        });
      }

      // Calculate subscription end date
      const subscriptionStart = new Date();
      const subscriptionEnd = new Date();
      subscriptionEnd.setDate(subscriptionEnd.getDate() + 30); // 30 days trial

      // Create center
      const center = await prisma.center.create({
        data: {
          name,
          address,
          phone,
          email,
          licenseNumber,
          licenseExpiry: licenseExpiry ? new Date(licenseExpiry) : null,
          subscriptionPlan,
          subscriptionStart,
          subscriptionEnd,
          ownerId: owner.id,
          isActive: true,
          isMainBranch: true,
          maxBranches: subscriptionPlan === 'PRO' ? 10 : 1
        },
        include: {
          owner: {
            select: {
              id: true,
              email: true,
              displayName: true
            }
          }
        }
      });

      logger.info(`Center created by admin: ${center.name} for owner: ${owner.email}`);

      return reply.code(201).send({
        success: true,
        message: 'Center created successfully',
        data: center
      });
    } catch (error) {
      logger.error('Create center error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Update center status
  fastify.patch('/centers/:id/status', {
    preHandler: [verifyAdmin],
    schema: {
      description: 'Update center status (Admin only)',
      tags: ['Admin'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        required: ['isActive'],
        properties: {
          isActive: { type: 'boolean' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      const { isActive } = request.body;

      const center = await prisma.center.update({
        where: { id },
        data: { isActive },
        include: {
          owner: {
            select: {
              id: true,
              email: true,
              displayName: true
            }
          }
        }
      });

      logger.info(`Center status updated by admin: ${center.name} - Active: ${isActive}`);

      return reply.send({
        success: true,
        message: 'Center status updated successfully',
        data: center
      });
    } catch (error) {
      logger.error('Update center status error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Get system statistics
  fastify.get('/stats', {
    preHandler: [verifyAdmin],
    schema: {
      description: 'Get system statistics (Admin only)',
      tags: ['Admin'],
      security: [{ bearerAuth: [] }]
    }
  }, async (request, reply) => {
    try {
      const [
        totalCenters,
        activeCenters,
        totalUsers,
        totalPatients,
        totalSessions,
        recentCenters
      ] = await Promise.all([
        prisma.center.count(),
        prisma.center.count({ where: { isActive: true } }),
        prisma.user.count(),
        prisma.patient.count(),
        prisma.session.count(),
        prisma.center.findMany({
          take: 5,
          orderBy: { createdAt: 'desc' },
          include: {
            owner: {
              select: {
                email: true,
                displayName: true
              }
            }
          }
        })
      ]);

      return reply.send({
        success: true,
        data: {
          totalCenters,
          activeCenters,
          totalUsers,
          totalPatients,
          totalSessions,
          recentCenters
        }
      });
    } catch (error) {
      logger.error('Get stats error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });
}
