import { NotificationService } from '../services/notificationService.js';
import { logger } from '../lib/logger.js';

/**
 * Notification management routes
 */
export default async function notificationRoutes(fastify, options) {

  // Get user notifications
  fastify.get('/', {
    preHandler: [fastify.authenticate],
    schema: {
      description: 'Get user notifications',
      tags: ['Notifications'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          offset: { type: 'integer', minimum: 0, default: 0 },
          unreadOnly: { type: 'boolean', default: false }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            notifications: { type: 'array' },
            pagination: { type: 'object' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { limit = 20, offset = 0, unreadOnly = false } = request.query;

      const result = await NotificationService.getUserNotifications(
        request.user.id,
        limit,
        offset,
        unreadOnly
      );

      return result;

    } catch (error) {
      logger.error('Failed to get notifications:', error);
      throw error;
    }
  });

  // Mark notification as read
  fastify.put('/:notificationId/read', {
    preHandler: [fastify.authenticate],
    schema: {
      description: 'Mark notification as read',
      tags: ['Notifications'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['notificationId'],
        properties: {
          notificationId: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            notification: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { notificationId } = request.params;

      const notification = await NotificationService.markAsRead(
        notificationId,
        request.user.id
      );

      return {
        notification,
        message: 'Notification marked as read'
      };

    } catch (error) {
      logger.error('Failed to mark notification as read:', error);
      throw error;
    }
  });

  // Mark all notifications as read
  fastify.put('/read-all', {
    preHandler: [fastify.authenticate],
    schema: {
      description: 'Mark all notifications as read',
      tags: ['Notifications'],
      security: [{ Bearer: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            count: { type: 'number' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const count = await NotificationService.markAllAsRead(request.user.id);

      return {
        count,
        message: `${count} notifications marked as read`
      };

    } catch (error) {
      logger.error('Failed to mark all notifications as read:', error);
      throw error;
    }
  });

  // Send test notification
  fastify.post('/test', {
    preHandler: [fastify.authenticate, fastify.authorize(['SUPER_ADMIN'])],
    schema: {
      description: 'Send test notification',
      tags: ['Notifications'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['userId', 'title', 'body'],
        properties: {
          userId: { type: 'string' },
          title: { type: 'string' },
          body: { type: 'string' },
          data: { type: 'object' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            result: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { userId, title, body, data = {} } = request.body;

      const result = await NotificationService.sendNotificationToUser(
        userId,
        title,
        body,
        data,
        'SYSTEM'
      );

      return {
        result,
        message: 'Test notification sent'
      };

    } catch (error) {
      logger.error('Failed to send test notification:', error);
      throw error;
    }
  });

  // Send appointment reminders (admin only)
  fastify.post('/reminders/appointments', {
    preHandler: [fastify.authenticate, fastify.authorize(['SUPER_ADMIN'])],
    schema: {
      description: 'Send appointment reminders',
      tags: ['Notifications'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          hoursAhead: { type: 'number', minimum: 1, maximum: 24, default: 2 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            results: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { hoursAhead = 2 } = request.body;

      const results = await NotificationService.sendAppointmentReminders(hoursAhead);

      return {
        results,
        message: 'Appointment reminders sent'
      };

    } catch (error) {
      logger.error('Failed to send appointment reminders:', error);
      throw error;
    }
  });
}
