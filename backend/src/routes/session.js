import { prisma } from '../config/database.js';
import { checkSubscriptionStatus, checkEntityLimit } from '../middleware/subscription.js';

/**
 * Session/Appointment management routes
 */
export default async function sessionRoutes(fastify, options) {

  // Get all sessions
  fastify.get('/', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'THERAPIST', 'PATIENT']),
      checkSubscriptionStatus
    ],
    schema: {
      description: 'Get all sessions based on user role',
      tags: ['Sessions'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          patientId: { type: 'string' },
          therapistId: { type: 'string' },
          status: {
            type: 'string',
            enum: ['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW']
          },
          type: {
            type: 'string',
            enum: ['CONSULTATION', 'THERAPY', 'FOLLOW_UP']
          },
          date: { type: 'string', format: 'date' },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          offset: { type: 'integer', minimum: 0, default: 0 },
          sortBy: { type: 'string', enum: ['scheduledAt', 'createdAt'], default: 'scheduledAt' },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' }
        }
      }
    }
  }, async (request, reply) => {
    const {
      patientId,
      therapistId,
      status,
      type,
      date,
      limit = 20,
      offset = 0,
      sortBy = 'scheduledAt',
      sortOrder = 'desc'
    } = request.query;

    const user = request.user;
    const center = request.center;

    // Build where clause based on user role
    let where = {};

    if (user.role === 'PATIENT') {
      // Patients can only see their own sessions
      const patient = await prisma.patient.findFirst({
        where: { userId: user.id }
      });
      if (!patient) {
        return { sessions: [], pagination: { total: 0, limit, offset, hasMore: false } };
      }
      where.patientId = patient.id;
    } else {
      // Center owners and therapists see sessions in their center
      where.centerId = center.id;

      if (user.role === 'THERAPIST') {
        // Therapists see only their assigned sessions
        const therapist = await prisma.therapist.findFirst({
          where: { userId: user.id, centerId: center.id }
        });
        if (therapist) {
          where.therapistId = therapist.id;
        }
      }
    }

    // Add filters
    if (patientId) where.patientId = patientId;
    if (therapistId) where.therapistId = therapistId;
    if (status) where.status = status;
    if (type) where.type = type;
    if (date) {
      const startDate = new Date(date);
      const endDate = new Date(date);
      endDate.setDate(endDate.getDate() + 1);
      where.scheduledAt = {
        gte: startDate,
        lt: endDate
      };
    }

    // Get sessions with pagination
    const [sessions, total] = await Promise.all([
      prisma.session.findMany({
        where,
        include: {
          patient: {
            select: {
              id: true,
              name: true,
              age: true,
              phone: true
            }
          },
          therapist: {
            include: {
              user: {
                select: {
                  displayName: true,
                  email: true
                }
              }
            }
          },
          center: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: { [sortBy]: sortOrder },
        take: limit,
        skip: offset
      }),
      prisma.session.count({ where })
    ]);

    return {
      sessions,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    };
  });

  // Create session
  fastify.post('/', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'THERAPIST']),
      checkSubscriptionStatus,
      checkEntityLimit('sessions')
    ],
    schema: {
      description: 'Create new session/appointment',
      tags: ['Sessions'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['patientId', 'therapistId', 'scheduledAt', 'duration', 'type'],
        properties: {
          patientId: { type: 'string' },
          therapistId: { type: 'string' },
          scheduledAt: { type: 'string', format: 'date-time' },
          duration: { type: 'integer', minimum: 15, maximum: 480 }, // 15 minutes to 8 hours
          type: {
            type: 'string',
            enum: ['CONSULTATION', 'THERAPY', 'FOLLOW_UP']
          },
          notes: { type: 'string', maxLength: 1000 },
          treatmentPlan: { type: 'string', maxLength: 2000 },
          fee: { type: 'number', minimum: 0 },
          isRecurring: { type: 'boolean', default: false },
          recurringPattern: {
            type: 'string',
            enum: ['DAILY', 'WEEKLY', 'BIWEEKLY', 'MONTHLY']
          },
          recurringCount: { type: 'integer', minimum: 1, maximum: 52 }
        }
      }
    }
  }, async (request, reply) => {
    const sessionData = request.body;
    const center = request.center;
    const user = request.user;

    // Validate patient belongs to center
    const patient = await prisma.patient.findFirst({
      where: {
        id: sessionData.patientId,
        centerId: center.id,
        isActive: true
      }
    });

    if (!patient) {
      return reply.status(404).send({ error: 'Patient not found or inactive' });
    }

    // Validate therapist belongs to center
    const therapist = await prisma.therapist.findFirst({
      where: {
        id: sessionData.therapistId,
        centerId: center.id,
        isActive: true
      }
    });

    if (!therapist) {
      return reply.status(404).send({ error: 'Therapist not found or inactive' });
    }

    // Check for scheduling conflicts
    const scheduledAt = new Date(sessionData.scheduledAt);
    const endTime = new Date(scheduledAt.getTime() + sessionData.duration * 60000);

    const conflictingSessions = await prisma.session.findMany({
      where: {
        therapistId: sessionData.therapistId,
        status: { in: ['SCHEDULED', 'IN_PROGRESS'] },
        OR: [
          {
            scheduledAt: {
              lt: endTime,
              gte: scheduledAt
            }
          },
          {
            AND: [
              { scheduledAt: { lte: scheduledAt } },
              {
                scheduledAt: {
                  gte: new Date(scheduledAt.getTime() - 60 * 60000) // 1 hour buffer
                }
              }
            ]
          }
        ]
      }
    });

    if (conflictingSessions.length > 0) {
      return reply.status(409).send({
        error: 'Scheduling conflict detected',
        conflictingSessions: conflictingSessions.map(s => ({
          id: s.id,
          scheduledAt: s.scheduledAt,
          duration: s.duration
        }))
      });
    }

    // Create session(s)
    const sessionsToCreate = [];

    if (sessionData.isRecurring && sessionData.recurringPattern && sessionData.recurringCount) {
      // Create recurring sessions
      let currentDate = new Date(scheduledAt);

      for (let i = 0; i < sessionData.recurringCount; i++) {
        sessionsToCreate.push({
          ...sessionData,
          scheduledAt: new Date(currentDate),
          centerId: center.id,
          createdBy: user.id,
          status: 'SCHEDULED'
        });

        // Calculate next occurrence
        switch (sessionData.recurringPattern) {
          case 'DAILY':
            currentDate.setDate(currentDate.getDate() + 1);
            break;
          case 'WEEKLY':
            currentDate.setDate(currentDate.getDate() + 7);
            break;
          case 'BIWEEKLY':
            currentDate.setDate(currentDate.getDate() + 14);
            break;
          case 'MONTHLY':
            currentDate.setMonth(currentDate.getMonth() + 1);
            break;
        }
      }
    } else {
      // Create single session
      sessionsToCreate.push({
        ...sessionData,
        centerId: center.id,
        createdBy: user.id,
        status: 'SCHEDULED'
      });
    }

    // Create sessions in database
    const createdSessions = await Promise.all(
      sessionsToCreate.map(sessionData =>
        prisma.session.create({
          data: sessionData,
          include: {
            patient: {
              select: {
                id: true,
                name: true,
                phone: true
              }
            },
            therapist: {
              include: {
                user: {
                  select: {
                    displayName: true
                  }
                }
              }
            }
          }
        })
      )
    );

    reply.status(201);
    return {
      message: `${createdSessions.length} session(s) created successfully`,
      sessions: createdSessions
    };
  });

  // Get session by ID
  fastify.get('/:sessionId', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'THERAPIST', 'PATIENT']),
      checkSubscriptionStatus
    ],
    schema: {
      description: 'Get session by ID with detailed information',
      tags: ['Sessions'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['sessionId'],
        properties: {
          sessionId: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    const { sessionId } = request.params;
    const user = request.user;
    const center = request.center;

    // Build where clause based on user role
    let where = { id: sessionId };

    if (user.role === 'PATIENT') {
      // Patients can only view their own sessions
      const patient = await prisma.patient.findFirst({
        where: { userId: user.id }
      });
      if (patient) {
        where.patientId = patient.id;
      } else {
        return reply.status(404).send({ error: 'Session not found' });
      }
    } else {
      // Center owners and therapists can view sessions in their center
      where.centerId = center.id;

      if (user.role === 'THERAPIST') {
        const therapist = await prisma.therapist.findFirst({
          where: { userId: user.id, centerId: center.id }
        });
        if (therapist) {
          where.therapistId = therapist.id;
        }
      }
    }

    const session = await prisma.session.findFirst({
      where,
      include: {
        patient: {
          select: {
            id: true,
            name: true,
            age: true,
            phone: true,
            email: true,
            medicalHistory: true,
            allergies: true,
            currentMedications: true
          }
        },
        therapist: {
          include: {
            user: {
              select: {
                displayName: true,
                email: true
              }
            }
          }
        },
        center: {
          select: {
            id: true,
            name: true,
            address: true
          }
        },
        progressNotes: {
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    if (!session) {
      return reply.status(404).send({ error: 'Session not found' });
    }

    return { session };
  });

  // Update session
  fastify.put('/:sessionId', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'THERAPIST']),
      checkSubscriptionStatus
    ],
    schema: {
      description: 'Update session information',
      tags: ['Sessions'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['sessionId'],
        properties: {
          sessionId: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        properties: {
          scheduledAt: { type: 'string', format: 'date-time' },
          duration: { type: 'integer', minimum: 15, maximum: 480 },
          type: {
            type: 'string',
            enum: ['CONSULTATION', 'THERAPY', 'FOLLOW_UP']
          },
          status: {
            type: 'string',
            enum: ['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW']
          },
          notes: { type: 'string', maxLength: 1000 },
          treatmentPlan: { type: 'string', maxLength: 2000 },
          fee: { type: 'number', minimum: 0 },
          actualStartTime: { type: 'string', format: 'date-time' },
          actualEndTime: { type: 'string', format: 'date-time' },
          cancellationReason: { type: 'string', maxLength: 500 }
        }
      }
    }
  }, async (request, reply) => {
    const { sessionId } = request.params;
    const updateData = request.body;
    const center = request.center;
    const user = request.user;

    // Check if session exists and belongs to the center
    const existingSession = await prisma.session.findFirst({
      where: {
        id: sessionId,
        centerId: center.id
      }
    });

    if (!existingSession) {
      return reply.status(404).send({ error: 'Session not found' });
    }

    // Validate status transitions
    const validTransitions = {
      'SCHEDULED': ['IN_PROGRESS', 'CANCELLED', 'NO_SHOW'],
      'IN_PROGRESS': ['COMPLETED', 'CANCELLED'],
      'COMPLETED': [], // Cannot change from completed
      'CANCELLED': ['SCHEDULED'], // Can reschedule
      'NO_SHOW': ['SCHEDULED'] // Can reschedule
    };

    if (updateData.status && updateData.status !== existingSession.status) {
      const allowedTransitions = validTransitions[existingSession.status] || [];
      if (!allowedTransitions.includes(updateData.status)) {
        return reply.status(400).send({
          error: `Cannot change status from ${existingSession.status} to ${updateData.status}`,
          allowedTransitions
        });
      }
    }

    // Auto-set timestamps based on status
    if (updateData.status === 'IN_PROGRESS' && !updateData.actualStartTime) {
      updateData.actualStartTime = new Date();
    }
    if (updateData.status === 'COMPLETED' && !updateData.actualEndTime) {
      updateData.actualEndTime = new Date();
    }

    // Update session
    const updatedSession = await prisma.session.update({
      where: { id: sessionId },
      data: {
        ...updateData,
        updatedBy: user.id,
        updatedAt: new Date()
      },
      include: {
        patient: {
          select: {
            id: true,
            name: true,
            phone: true
          }
        },
        therapist: {
          include: {
            user: {
              select: {
                displayName: true
              }
            }
          }
        }
      }
    });

    return {
      message: 'Session updated successfully',
      session: updatedSession
    };
  });

  // Add progress note to session
  fastify.post('/:sessionId/progress', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'THERAPIST']),
      checkSubscriptionStatus
    ],
    schema: {
      description: 'Add progress note to session',
      tags: ['Sessions'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['sessionId'],
        properties: {
          sessionId: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        required: ['notes'],
        properties: {
          notes: { type: 'string', minLength: 1, maxLength: 2000 },
          painScore: { type: 'integer', minimum: 0, maximum: 10 },
          exercisesCompleted: { type: 'array', items: { type: 'string' } },
          nextSessionGoals: { type: 'string', maxLength: 1000 },
          homeExercises: { type: 'string', maxLength: 1000 },
          improvementNotes: { type: 'string', maxLength: 1000 }
        }
      }
    }
  }, async (request, reply) => {
    const { sessionId } = request.params;
    const progressData = request.body;
    const center = request.center;
    const user = request.user;

    // Check if session exists and belongs to the center
    const session = await prisma.session.findFirst({
      where: {
        id: sessionId,
        centerId: center.id
      }
    });

    if (!session) {
      return reply.status(404).send({ error: 'Session not found' });
    }

    // Create progress note
    const progressNote = await prisma.progressNote.create({
      data: {
        ...progressData,
        sessionId,
        createdBy: user.id
      }
    });

    return {
      message: 'Progress note added successfully',
      progressNote
    };
  });

  // Delete session
  fastify.delete('/:sessionId', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'THERAPIST']),
      checkSubscriptionStatus
    ],
    schema: {
      description: 'Delete/Cancel session',
      tags: ['Sessions'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['sessionId'],
        properties: {
          sessionId: { type: 'string' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          reason: { type: 'string', maxLength: 500 }
        }
      }
    }
  }, async (request, reply) => {
    const { sessionId } = request.params;
    const { reason } = request.query;
    const center = request.center;

    // Check if session exists and belongs to the center
    const session = await prisma.session.findFirst({
      where: {
        id: sessionId,
        centerId: center.id
      },
      include: {
        progressNotes: true
      }
    });

    if (!session) {
      return reply.status(404).send({ error: 'Session not found' });
    }

    // If session has progress notes or is completed, just cancel it
    if (session.progressNotes.length > 0 || session.status === 'COMPLETED') {
      await prisma.session.update({
        where: { id: sessionId },
        data: {
          status: 'CANCELLED',
          cancellationReason: reason,
          updatedAt: new Date()
        }
      });

      return {
        message: 'Session cancelled successfully (has progress notes)'
      };
    } else {
      // Hard delete if no progress notes
      await prisma.session.delete({
        where: { id: sessionId }
      });

      return {
        message: 'Session deleted successfully'
      };
    }
  });

  // Get session statistics
  fastify.get('/stats/overview', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'THERAPIST']),
      checkSubscriptionStatus
    ],
    schema: {
      description: 'Get session statistics overview',
      tags: ['Sessions'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    const center = request.center;
    const user = request.user;
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());

    // Build where clause based on user role
    let where = { centerId: center.id };
    if (user.role === 'THERAPIST') {
      const therapist = await prisma.therapist.findFirst({
        where: { userId: user.id, centerId: center.id }
      });
      if (therapist) {
        where.therapistId = therapist.id;
      }
    }

    // Get basic counts
    const [
      totalSessions,
      scheduledSessions,
      completedSessions,
      cancelledSessions,
      thisWeekSessions,
      thisMonthSessions
    ] = await Promise.all([
      prisma.session.count({ where }),
      prisma.session.count({ where: { ...where, status: 'SCHEDULED' } }),
      prisma.session.count({ where: { ...where, status: 'COMPLETED' } }),
      prisma.session.count({ where: { ...where, status: 'CANCELLED' } }),
      prisma.session.count({
        where: {
          ...where,
          scheduledAt: { gte: startOfWeek }
        }
      }),
      prisma.session.count({
        where: {
          ...where,
          scheduledAt: { gte: startOfMonth }
        }
      })
    ]);

    // Get upcoming sessions
    const upcomingSessions = await prisma.session.findMany({
      where: {
        ...where,
        status: 'SCHEDULED',
        scheduledAt: { gte: now }
      },
      include: {
        patient: {
          select: {
            id: true,
            name: true
          }
        },
        therapist: {
          include: {
            user: {
              select: {
                displayName: true
              }
            }
          }
        }
      },
      orderBy: { scheduledAt: 'asc' },
      take: 5
    });

    return {
      stats: {
        total: totalSessions,
        scheduled: scheduledSessions,
        completed: completedSessions,
        cancelled: cancelledSessions,
        thisWeek: thisWeekSessions,
        thisMonth: thisMonthSessions,
        upcomingSessions
      }
    };
  });
}
