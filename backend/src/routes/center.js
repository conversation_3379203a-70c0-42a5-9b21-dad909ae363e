/**
 * Center management routes
 * TODO: Implement center CRUD operations
 */
export default async function centerRoutes(fastify, options) {
  
  // Get all centers (admin only)
  fastify.get('/', {
    preHandler: [fastify.authenticate, fastify.authorize(['SUPER_ADMIN'])],
    schema: {
      description: 'Get all centers',
      tags: ['Centers'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    return { message: 'Center routes - TODO: Implement' };
  });

  // Create center
  fastify.post('/', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN'])],
    schema: {
      description: 'Create new center',
      tags: ['Centers'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    return { message: 'Create center - TODO: Implement' };
  });

  // Get center by ID
  fastify.get('/:centerId', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'THERAPIST', 'SUPER_ADMIN'])],
    schema: {
      description: 'Get center by ID',
      tags: ['Centers'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    return { message: 'Get center - TODO: Implement' };
  });

  // Update center
  fastify.put('/:centerId', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN'])],
    schema: {
      description: 'Update center',
      tags: ['Centers'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    return { message: 'Update center - TODO: Implement' };
  });

  // Delete center
  fastify.delete('/:centerId', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN'])],
    schema: {
      description: 'Delete center',
      tags: ['Centers'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    return { message: 'Delete center - TODO: Implement' };
  });
}
