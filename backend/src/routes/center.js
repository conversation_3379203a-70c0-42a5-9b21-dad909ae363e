import { prisma } from '../lib/prisma.js';
import { logger } from '../lib/logger.js';
import { requireFeature, checkLimit, countFunctions } from '../middleware/subscriptionMiddleware.js';

/**
 * Center management routes
 */
export default async function centerRoutes(fastify, options) {

  // Get all centers (admin only)
  fastify.get('/', {
    preHandler: [fastify.authenticate, fastify.authorize(['SUPER_ADMIN'])],
    schema: {
      description: 'Get all centers',
      tags: ['Centers'],
      security: [{ Bearer: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          search: { type: 'string' },
          plan: { type: 'string', enum: ['TRIAL', 'BASIC', 'PRO', 'ENTERPRISE'] },
          active: { type: 'boolean' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { page = 1, limit = 20, search, plan, active } = request.query;
      const skip = (page - 1) * limit;

      const where = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { address: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (plan) {
        where.subscriptionPlan = plan;
      }

      if (active !== undefined) {
        where.isActive = active;
      }

      const [centers, total] = await Promise.all([
        prisma.center.findMany({
          where,
          include: {
            owner: {
              select: { id: true, displayName: true, email: true }
            },
            _count: {
              select: {
                therapists: true,
                patients: true,
                employees: true,
                branches: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip
        }),
        prisma.center.count({ where })
      ]);

      return {
        centers,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      };

    } catch (error) {
      logger.error('Get centers error:', error);
      throw error;
    }
  });

  // Get user's centers
  fastify.get('/my-centers', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER'])],
    schema: {
      description: 'Get current user centers',
      tags: ['Centers'],
      security: [{ Bearer: [] }]
    }
  }, async (request, reply) => {
    try {
      const centers = await prisma.center.findMany({
        where: {
          ownerId: request.user.id,
          isActive: true
        },
        include: {
          branches: {
            where: { isActive: true },
            select: {
              id: true,
              name: true,
              address: true,
              branchCode: true,
              isActive: true
            }
          },
          _count: {
            select: {
              therapists: true,
              patients: true,
              employees: true,
              sessions: true
            }
          }
        },
        orderBy: [
          { isMainBranch: 'desc' },
          { createdAt: 'asc' }
        ]
      });

      return { centers };

    } catch (error) {
      logger.error('Get my centers error:', error);
      throw error;
    }
  });

  // Create center
  fastify.post('/', {
    preHandler: [
      fastify.authenticate,
      fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN']),
      checkLimit('maxBranches', countFunctions.maxBranches)
    ],
    schema: {
      description: 'Create new center',
      tags: ['Centers'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        required: ['name', 'address'],
        properties: {
          name: { type: 'string', minLength: 2, maxLength: 100 },
          address: { type: 'string', minLength: 5, maxLength: 500 },
          phone: { type: 'string' },
          email: { type: 'string', format: 'email' },
          licenseNumber: { type: 'string' },
          licenseExpiry: { type: 'string', format: 'date' },
          subscriptionPlan: { type: 'string', enum: ['TRIAL', 'BASIC', 'PRO', 'ENTERPRISE'] },
          parentCenterId: { type: 'string' }, // For creating branches
          isMainBranch: { type: 'boolean', default: true }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const {
        name,
        address,
        phone,
        email,
        licenseNumber,
        licenseExpiry,
        subscriptionPlan = 'TRIAL',
        parentCenterId,
        isMainBranch = true
      } = request.body;

      // If creating a branch, check multi-branch feature
      if (parentCenterId) {
        await requireFeature('multiBranch')(request, reply);
        if (reply.sent) return;
      }

      // Generate branch code
      const centerCount = await prisma.center.count({
        where: { ownerId: request.user.id }
      });
      const branchCode = `BR-${String(centerCount + 1).padStart(3, '0')}`;

      // Set subscription dates
      const subscriptionStart = new Date();
      let subscriptionEnd = null;

      if (subscriptionPlan === 'TRIAL') {
        subscriptionEnd = new Date();
        subscriptionEnd.setDate(subscriptionEnd.getDate() + 30);
      } else {
        subscriptionEnd = new Date();
        subscriptionEnd.setFullYear(subscriptionEnd.getFullYear() + 1);
      }

      const center = await prisma.center.create({
        data: {
          name,
          address,
          phone,
          email,
          licenseNumber,
          licenseExpiry: licenseExpiry ? new Date(licenseExpiry) : null,
          subscriptionPlan,
          subscriptionStart,
          subscriptionEnd,
          isMainBranch: parentCenterId ? false : isMainBranch,
          parentCenterId,
          branchCode,
          ownerId: request.user.id
        },
        include: {
          owner: {
            select: { id: true, displayName: true, email: true }
          },
          parentCenter: {
            select: { id: true, name: true }
          }
        }
      });

      logger.info(`Center created: ${center.name} by user ${request.user.email}`);

      return {
        center,
        message: 'Center created successfully'
      };

    } catch (error) {
      logger.error('Create center error:', error);
      throw error;
    }
  });

  // Get center by ID
  fastify.get('/:centerId', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'THERAPIST', 'SUPER_ADMIN'])],
    schema: {
      description: 'Get center by ID',
      tags: ['Centers'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['centerId'],
        properties: {
          centerId: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { centerId } = request.params;

      const center = await prisma.center.findUnique({
        where: { id: centerId },
        include: {
          owner: {
            select: { id: true, displayName: true, email: true }
          },
          parentCenter: {
            select: { id: true, name: true }
          },
          branches: {
            where: { isActive: true },
            select: {
              id: true,
              name: true,
              address: true,
              branchCode: true,
              isActive: true
            }
          },
          _count: {
            select: {
              therapists: true,
              patients: true,
              employees: true,
              sessions: true
            }
          }
        }
      });

      if (!center) {
        reply.code(404);
        return {
          error: 'Not Found',
          message: 'Center not found',
          statusCode: 404,
          timestamp: new Date().toISOString()
        };
      }

      // Check access permissions
      if (request.user.role === 'CENTER_OWNER' && center.ownerId !== request.user.id) {
        reply.code(403);
        return {
          error: 'Forbidden',
          message: 'Access denied to this center',
          statusCode: 403,
          timestamp: new Date().toISOString()
        };
      }

      if (request.user.role === 'THERAPIST') {
        const hasAccess = request.user.therapistProfile?.centerId === centerId;
        if (!hasAccess) {
          reply.code(403);
          return {
            error: 'Forbidden',
            message: 'Access denied to this center',
            statusCode: 403,
            timestamp: new Date().toISOString()
          };
        }
      }

      return { center };

    } catch (error) {
      logger.error('Get center error:', error);
      throw error;
    }
  });

  // Update center
  fastify.put('/:centerId', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN'])],
    schema: {
      description: 'Update center',
      tags: ['Centers'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['centerId'],
        properties: {
          centerId: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        properties: {
          name: { type: 'string', minLength: 2, maxLength: 100 },
          address: { type: 'string', minLength: 5, maxLength: 500 },
          phone: { type: 'string' },
          email: { type: 'string', format: 'email' },
          licenseNumber: { type: 'string' },
          licenseExpiry: { type: 'string', format: 'date' },
          isActive: { type: 'boolean' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { centerId } = request.params;
      const updateData = request.body;

      // Check if center exists and user has access
      const existingCenter = await prisma.center.findUnique({
        where: { id: centerId }
      });

      if (!existingCenter) {
        reply.code(404);
        return {
          error: 'Not Found',
          message: 'Center not found',
          statusCode: 404,
          timestamp: new Date().toISOString()
        };
      }

      if (request.user.role === 'CENTER_OWNER' && existingCenter.ownerId !== request.user.id) {
        reply.code(403);
        return {
          error: 'Forbidden',
          message: 'Access denied to this center',
          statusCode: 403,
          timestamp: new Date().toISOString()
        };
      }

      // Prepare update data
      const processedData = { ...updateData };
      if (processedData.licenseExpiry) {
        processedData.licenseExpiry = new Date(processedData.licenseExpiry);
      }

      const updatedCenter = await prisma.center.update({
        where: { id: centerId },
        data: processedData,
        include: {
          owner: {
            select: { id: true, displayName: true, email: true }
          },
          parentCenter: {
            select: { id: true, name: true }
          }
        }
      });

      logger.info(`Center updated: ${updatedCenter.name} by user ${request.user.email}`);

      return {
        center: updatedCenter,
        message: 'Center updated successfully'
      };

    } catch (error) {
      logger.error('Update center error:', error);
      throw error;
    }
  });

  // Delete center
  fastify.delete('/:centerId', {
    preHandler: [fastify.authenticate, fastify.authorize(['CENTER_OWNER', 'SUPER_ADMIN'])],
    schema: {
      description: 'Delete center',
      tags: ['Centers'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        required: ['centerId'],
        properties: {
          centerId: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { centerId } = request.params;

      // Check if center exists and user has access
      const existingCenter = await prisma.center.findUnique({
        where: { id: centerId },
        include: {
          branches: true,
          therapists: true,
          patients: true,
          sessions: true
        }
      });

      if (!existingCenter) {
        reply.code(404);
        return {
          error: 'Not Found',
          message: 'Center not found',
          statusCode: 404,
          timestamp: new Date().toISOString()
        };
      }

      if (request.user.role === 'CENTER_OWNER' && existingCenter.ownerId !== request.user.id) {
        reply.code(403);
        return {
          error: 'Forbidden',
          message: 'Access denied to this center',
          statusCode: 403,
          timestamp: new Date().toISOString()
        };
      }

      // Check if center has dependencies
      if (existingCenter.branches.length > 0) {
        reply.code(400);
        return {
          error: 'Bad Request',
          message: 'Cannot delete center with active branches. Delete branches first.',
          statusCode: 400,
          timestamp: new Date().toISOString()
        };
      }

      if (existingCenter.sessions.length > 0) {
        reply.code(400);
        return {
          error: 'Bad Request',
          message: 'Cannot delete center with existing sessions. Archive the center instead.',
          statusCode: 400,
          timestamp: new Date().toISOString()
        };
      }

      // Soft delete by marking as inactive
      await prisma.center.update({
        where: { id: centerId },
        data: { isActive: false }
      });

      logger.info(`Center deleted: ${existingCenter.name} by user ${request.user.email}`);

      return {
        message: 'Center deleted successfully'
      };

    } catch (error) {
      logger.error('Delete center error:', error);
      throw error;
    }
  });
}
