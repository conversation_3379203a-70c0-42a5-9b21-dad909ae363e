import { prisma } from '../lib/prisma.js';
import { logger } from '../lib/logger.js';
import { hasFeature, getFeatureLimit, isLimitExceeded } from '../config/subscriptionFeatures.js';

/**
 * Middleware to check subscription-based feature access
 * @param {string|string[]} requiredFeatures - Feature(s) required for this endpoint
 * @param {Object} options - Additional options
 * @returns {Function} Middleware function
 */
export function requireFeature(requiredFeatures, options = {}) {
  const features = Array.isArray(requiredFeatures) ? requiredFeatures : [requiredFeatures];
  
  return async function(request, reply) {
    try {
      if (!request.user) {
        reply.code(401).send({
          error: 'Unauthorized',
          message: 'Authentication required',
          statusCode: 401,
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Get user's center and subscription plan
      let userCenter = null;
      let subscriptionPlan = 'TRIAL';

      if (request.user.role === 'CENTER_OWNER') {
        // For center owners, get their primary center or the one being accessed
        if (request.params.centerId) {
          userCenter = request.user.ownedCenters.find(center => center.id === request.params.centerId);
        } else {
          userCenter = request.user.ownedCenters[0]; // Primary center
        }
      } else if (request.user.role === 'THERAPIST') {
        userCenter = request.user.therapistProfile?.center;
      } else if (request.user.role === 'EMPLOYEE') {
        userCenter = request.user.employeeProfile?.center;
      }

      if (userCenter) {
        subscriptionPlan = userCenter.subscriptionPlan;
        
        // Check if subscription is active
        if (userCenter.subscriptionEnd && new Date(userCenter.subscriptionEnd) < new Date()) {
          reply.code(402).send({
            error: 'Subscription Expired',
            message: 'Your subscription has expired. Please renew to continue using this feature.',
            statusCode: 402,
            timestamp: new Date().toISOString()
          });
          return;
        }
      }

      // Super admins bypass all restrictions
      if (request.user.role === 'SUPER_ADMIN') {
        return;
      }

      // Check each required feature
      for (const feature of features) {
        if (!hasFeature(subscriptionPlan, feature)) {
          reply.code(402).send({
            error: 'Feature Not Available',
            message: `This feature requires a higher subscription plan. Current plan: ${subscriptionPlan}`,
            statusCode: 402,
            timestamp: new Date().toISOString(),
            requiredFeature: feature,
            currentPlan: subscriptionPlan
          });
          return;
        }
      }

      // Add subscription context to request
      request.subscriptionPlan = subscriptionPlan;
      request.userCenter = userCenter;

    } catch (error) {
      logger.error('Subscription middleware error:', error);
      reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Failed to check subscription status',
        statusCode: 500,
        timestamp: new Date().toISOString()
      });
    }
  };
}

/**
 * Middleware to check usage limits
 * @param {string} limitType - Type of limit to check (e.g., 'maxPatients', 'maxTherapists')
 * @param {Function} countFunction - Function to get current count
 * @returns {Function} Middleware function
 */
export function checkLimit(limitType, countFunction) {
  return async function(request, reply) {
    try {
      if (!request.user || !request.userCenter) {
        // Skip limit check if no user context
        return;
      }

      // Super admins bypass all limits
      if (request.user.role === 'SUPER_ADMIN') {
        return;
      }

      const subscriptionPlan = request.subscriptionPlan;
      const limit = getFeatureLimit(subscriptionPlan, limitType);
      
      // -1 means unlimited
      if (limit === -1) {
        return;
      }

      // Get current count
      const currentCount = await countFunction(request);
      
      if (isLimitExceeded(subscriptionPlan, limitType, currentCount)) {
        reply.code(402).send({
          error: 'Limit Exceeded',
          message: `You have reached the limit for ${limitType}. Current: ${currentCount}, Limit: ${limit}`,
          statusCode: 402,
          timestamp: new Date().toISOString(),
          limitType,
          currentCount,
          limit,
          currentPlan: subscriptionPlan
        });
        return;
      }

    } catch (error) {
      logger.error('Limit check middleware error:', error);
      reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Failed to check usage limits',
        statusCode: 500,
        timestamp: new Date().toISOString()
      });
    }
  };
}

/**
 * Common count functions for limits
 */
export const countFunctions = {
  // Count patients for a center
  maxPatients: async (request) => {
    const centerId = request.userCenter?.id;
    if (!centerId) return 0;
    
    return await prisma.patient.count({
      where: { centerId, isActive: true }
    });
  },

  // Count therapists for a center
  maxTherapists: async (request) => {
    const centerId = request.userCenter?.id;
    if (!centerId) return 0;
    
    return await prisma.therapist.count({
      where: { centerId, isActive: true }
    });
  },

  // Count employees for a center
  maxEmployees: async (request) => {
    const centerId = request.userCenter?.id;
    if (!centerId) return 0;
    
    return await prisma.employee.count({
      where: { centerId, status: 'ACTIVE' }
    });
  },

  // Count branches for a center owner
  maxBranches: async (request) => {
    const ownerId = request.user?.id;
    if (!ownerId) return 0;
    
    return await prisma.center.count({
      where: { ownerId, isActive: true }
    });
  },

  // Count sessions for current month
  maxSessions: async (request) => {
    const centerId = request.userCenter?.id;
    if (!centerId) return 0;
    
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);
    
    const endOfMonth = new Date(startOfMonth);
    endOfMonth.setMonth(endOfMonth.getMonth() + 1);
    
    return await prisma.session.count({
      where: {
        centerId,
        scheduledAt: {
          gte: startOfMonth,
          lt: endOfMonth
        }
      }
    });
  }
};

/**
 * Helper function to get subscription info for a user
 * @param {Object} user - User object
 * @returns {Object} Subscription information
 */
export async function getUserSubscriptionInfo(user) {
  try {
    let center = null;
    
    if (user.role === 'CENTER_OWNER' && user.ownedCenters?.length > 0) {
      center = user.ownedCenters[0]; // Primary center
    } else if (user.role === 'THERAPIST' && user.therapistProfile?.center) {
      center = user.therapistProfile.center;
    } else if (user.role === 'EMPLOYEE' && user.employeeProfile?.center) {
      center = user.employeeProfile.center;
    }

    if (!center) {
      return {
        plan: 'TRIAL',
        isActive: false,
        expiresAt: null,
        features: {},
        limits: {}
      };
    }

    const isActive = !center.subscriptionEnd || new Date(center.subscriptionEnd) > new Date();
    
    return {
      plan: center.subscriptionPlan,
      isActive,
      expiresAt: center.subscriptionEnd,
      center: {
        id: center.id,
        name: center.name,
        isMainBranch: center.isMainBranch
      }
    };

  } catch (error) {
    logger.error('Get subscription info error:', error);
    return {
      plan: 'TRIAL',
      isActive: false,
      expiresAt: null,
      features: {},
      limits: {}
    };
  }
}
