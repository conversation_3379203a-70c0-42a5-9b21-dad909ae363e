import { prisma } from '../config/database.js';

// Subscription plan limits and pricing
const PLAN_LIMITS = {
  TRIAL: {
    name: 'Trial',
    price: 0,
    currency: 'BDT',
    duration: 30, // days
    maxPatients: 10,
    maxTherapists: 2,
    maxEmployees: 3,
    maxBranches: 1,
    maxSessions: 50,
    maxStorage: 100, // MB
    features: {
      hrmModule: false,
      accountingModule: false,
      multiBranch: false,
      advancedReports: false,
      apiAccess: false,
      prioritySupport: false,
      customBranding: false,
      dataExport: false,
      bulkOperations: false,
      automatedBackups: false,
    }
  },
  BASIC: {
    name: 'Basic',
    price: 1500,
    currency: 'BDT',
    duration: null, // unlimited
    maxPatients: 100,
    maxTherapists: 5,
    maxEmployees: 10,
    maxBranches: 1,
    maxSessions: 1000,
    maxStorage: 1000, // MB
    features: {
      hrmModule: true,
      accountingModule: false,
      multiBranch: false,
      advancedReports: true,
      apiAccess: false,
      prioritySupport: false,
      customBranding: false,
      dataExport: true,
      bulkOperations: true,
      automatedBackups: true,
    }
  },
  PRO: {
    name: 'Pro',
    price: 3000,
    currency: 'BDT',
    duration: null,
    maxPatients: 500,
    maxTherapists: 20,
    maxEmployees: 50,
    maxBranches: 5,
    maxSessions: 5000,
    maxStorage: 5000, // MB
    features: {
      hrmModule: true,
      accountingModule: true,
      multiBranch: true,
      advancedReports: true,
      apiAccess: true,
      prioritySupport: true,
      customBranding: false,
      dataExport: true,
      bulkOperations: true,
      automatedBackups: true,
    }
  },
  ENTERPRISE: {
    name: 'Enterprise',
    price: 5000,
    currency: 'BDT',
    duration: null,
    maxPatients: -1, // Unlimited
    maxTherapists: -1,
    maxEmployees: -1,
    maxBranches: -1,
    maxSessions: -1,
    maxStorage: -1,
    features: {
      hrmModule: true,
      accountingModule: true,
      multiBranch: true,
      advancedReports: true,
      apiAccess: true,
      prioritySupport: true,
      customBranding: true,
      dataExport: true,
      bulkOperations: true,
      automatedBackups: true,
    }
  }
};

// Get plan information
export const getPlanLimits = (plan) => {
  return PLAN_LIMITS[plan] || PLAN_LIMITS.TRIAL;
};

// Get all available plans
export const getAllPlans = () => {
  return PLAN_LIMITS;
};

// Check if subscription is active and valid
export const checkSubscriptionStatus = async (req, res, next) => {
  try {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Super admin bypasses subscription checks
    if (user.role === 'SUPER_ADMIN') {
      req.planLimits = PLAN_LIMITS.ENTERPRISE; // Give super admin full access
      return next();
    }

    // Get user's center with subscription details
    const center = await prisma.center.findFirst({
      where: {
        OR: [
          { ownerId: user.id },
          { therapists: { some: { userId: user.id } } },
          { employees: { some: { userId: user.id } } }
        ]
      },
      include: {
        _count: {
          select: {
            patients: true,
            therapists: true,
            employees: true,
            branches: true,
            sessions: true,
          }
        }
      }
    });

    if (!center) {
      return res.status(403).json({ error: 'No center associated with user' });
    }

    // Check if subscription is expired
    const now = new Date();
    if (center.subscriptionEnd && now > center.subscriptionEnd) {
      // Grace period of 7 days for expired subscriptions
      const gracePeriodEnd = new Date(center.subscriptionEnd);
      gracePeriodEnd.setDate(gracePeriodEnd.getDate() + 7);
      
      if (now > gracePeriodEnd) {
        return res.status(403).json({ 
          error: 'Subscription expired',
          subscriptionEnd: center.subscriptionEnd,
          gracePeriodEnd: gracePeriodEnd,
          message: 'Please renew your subscription to continue using the service'
        });
      } else {
        // In grace period - add warning
        req.subscriptionWarning = {
          message: 'Subscription expired - grace period active',
          daysRemaining: Math.ceil((gracePeriodEnd - now) / (1000 * 60 * 60 * 24))
        };
      }
    }

    // Add center and plan info to request
    req.center = center;
    req.planLimits = PLAN_LIMITS[center.subscriptionPlan] || PLAN_LIMITS.TRIAL;
    req.currentUsage = center._count;
    
    next();
  } catch (error) {
    console.error('Subscription check error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Check feature access
export const requireFeature = (feature) => {
  return (req, res, next) => {
    const planLimits = req.planLimits;
    
    if (!planLimits || !planLimits.features[feature]) {
      return res.status(403).json({ 
        error: `Feature '${feature}' not available in your subscription plan`,
        currentPlan: req.center?.subscriptionPlan,
        requiredPlans: Object.keys(PLAN_LIMITS).filter(
          plan => PLAN_LIMITS[plan].features[feature]
        ),
        upgradeUrl: '/subscription/upgrade'
      });
    }
    
    next();
  };
};

// Check entity limits before creation
export const checkEntityLimit = (entityType) => {
  return async (req, res, next) => {
    try {
      const center = req.center;
      const planLimits = req.planLimits;
      const currentUsage = req.currentUsage;
      
      if (!center || !planLimits) {
        return res.status(403).json({ error: 'Subscription validation failed' });
      }

      const limitKey = `max${entityType.charAt(0).toUpperCase() + entityType.slice(1)}`;
      const maxLimit = planLimits[limitKey];
      
      // Unlimited (-1) means no limit
      if (maxLimit === -1) {
        return next();
      }

      // Get current count from usage data or query database
      let currentCount = currentUsage?.[entityType] || 0;
      
      if (currentCount >= maxLimit) {
        return res.status(403).json({ 
          error: `${entityType} limit reached`,
          currentCount,
          maxLimit,
          plan: center.subscriptionPlan,
          upgradeUrl: '/subscription/upgrade',
          message: `You have reached the maximum number of ${entityType} allowed in your ${center.subscriptionPlan} plan. Please upgrade to add more.`
        });
      }

      // Add usage info to request for potential use in response
      req.usageInfo = {
        [entityType]: {
          current: currentCount,
          max: maxLimit,
          remaining: maxLimit - currentCount,
          percentage: Math.round((currentCount / maxLimit) * 100)
        }
      };

      next();
    } catch (error) {
      console.error('Entity limit check error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  };
};

// Middleware to add subscription info to responses
export const addSubscriptionInfo = (req, res, next) => {
  const originalJson = res.json;
  
  res.json = function(data) {
    if (req.center && req.planLimits) {
      data.subscription = {
        plan: req.center.subscriptionPlan,
        planName: req.planLimits.name,
        subscriptionEnd: req.center.subscriptionEnd,
        isActive: !req.center.subscriptionEnd || new Date() <= req.center.subscriptionEnd,
        features: req.planLimits.features,
        limits: {
          patients: { max: req.planLimits.maxPatients, current: req.currentUsage?.patients || 0 },
          therapists: { max: req.planLimits.maxTherapists, current: req.currentUsage?.therapists || 0 },
          employees: { max: req.planLimits.maxEmployees, current: req.currentUsage?.employees || 0 },
          branches: { max: req.planLimits.maxBranches, current: req.currentUsage?.branches || 0 },
          sessions: { max: req.planLimits.maxSessions, current: req.currentUsage?.sessions || 0 },
          storage: { max: req.planLimits.maxStorage, current: 0 },
        }
      };
      
      if (req.subscriptionWarning) {
        data.subscription.warning = req.subscriptionWarning;
      }
      
      if (req.usageInfo) {
        data.usageInfo = req.usageInfo;
      }
    }
    
    return originalJson.call(this, data);
  };
  
  next();
};
