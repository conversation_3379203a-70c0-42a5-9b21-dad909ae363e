import { prisma } from '../lib/prisma.js';
import { verifyIdToken, getFirebaseUser } from '../lib/firebase.js';
import { logger } from '../lib/logger.js';

/**
 * Authentication service
 * Handles user authentication and registration logic
 */
export class AuthService {
  
  /**
   * Login or register user with Firebase token
   * @param {string} idToken - Firebase ID token
   * @param {string} fcmToken - FCM token for push notifications
   * @param {string} role - User role for new users
   * @returns {Promise<{user: Object, isNewUser: boolean}>}
   */
  static async loginOrRegister(idToken, fcmToken = null, role = 'CENTER_OWNER') {
    try {
      // Verify Firebase token
      const decodedToken = await verifyIdToken(idToken);
      
      if (!decodedToken) {
        throw new Error('Invalid Firebase token');
      }

      // Check if user exists in our database
      let user = await prisma.user.findUnique({
        where: { firebaseUid: decodedToken.uid },
        include: {
          ownedCenters: {
            select: {
              id: true,
              name: true,
              subscriptionPlan: true,
              subscriptionEnd: true,
              isActive: true
            }
          },
          therapistProfile: {
            include: {
              center: {
                select: {
                  id: true,
                  name: true,
                  subscriptionPlan: true,
                  subscriptionEnd: true
                }
              }
            }
          },
          patientProfile: {
            include: {
              center: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });

      let isNewUser = false;

      if (!user) {
        // Get additional user info from Firebase
        const firebaseUser = await getFirebaseUser(decodedToken.uid);
        
        // Create new user in our database
        user = await prisma.user.create({
          data: {
            firebaseUid: decodedToken.uid,
            email: decodedToken.email || firebaseUser.email,
            displayName: decodedToken.name || firebaseUser.displayName || decodedToken.email?.split('@')[0],
            photoURL: decodedToken.picture || firebaseUser.photoURL,
            role: role,
            fcmToken: fcmToken,
            isActive: true
          },
          include: {
            ownedCenters: {
              select: {
                id: true,
                name: true,
                subscriptionPlan: true,
                subscriptionEnd: true,
                isActive: true
              }
            },
            therapistProfile: {
              include: {
                center: {
                  select: {
                    id: true,
                    name: true,
                    subscriptionPlan: true,
                    subscriptionEnd: true
                  }
                }
              }
            },
            patientProfile: {
              include: {
                center: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          }
        });

        isNewUser = true;
        logger.info(`New user registered: ${user.email} with role: ${role}`);
      } else {
        // Update existing user's FCM token and last login
        if (fcmToken && user.fcmToken !== fcmToken) {
          user = await prisma.user.update({
            where: { id: user.id },
            data: { 
              fcmToken,
              updatedAt: new Date()
            },
            include: {
              ownedCenters: {
                select: {
                  id: true,
                  name: true,
                  subscriptionPlan: true,
                  subscriptionEnd: true,
                  isActive: true
                }
              },
              therapistProfile: {
                include: {
                  center: {
                    select: {
                      id: true,
                      name: true,
                      subscriptionPlan: true,
                      subscriptionEnd: true
                    }
                  }
                }
              },
              patientProfile: {
                include: {
                  center: {
                    select: {
                      id: true,
                      name: true
                    }
                  }
                }
              }
            }
          });
        }
        logger.info(`User logged in: ${user.email}`);
      }

      // Check if user account is active
      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      return { user, isNewUser };

    } catch (error) {
      logger.error('Login/Register failed:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated user
   */
  static async updateProfile(userId, updateData) {
    try {
      const allowedFields = ['displayName', 'fcmToken'];
      const filteredData = {};

      // Only allow specific fields to be updated
      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          filteredData[field] = updateData[field];
        }
      }

      if (Object.keys(filteredData).length === 0) {
        throw new Error('No valid fields to update');
      }

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          ...filteredData,
          updatedAt: new Date()
        },
        include: {
          ownedCenters: {
            select: {
              id: true,
              name: true,
              subscriptionPlan: true,
              subscriptionEnd: true,
              isActive: true
            }
          },
          therapistProfile: {
            include: {
              center: {
                select: {
                  id: true,
                  name: true,
                  subscriptionPlan: true,
                  subscriptionEnd: true
                }
              }
            }
          },
          patientProfile: {
            include: {
              center: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });

      logger.info(`User profile updated: ${updatedUser.email}`);
      return updatedUser;

    } catch (error) {
      logger.error('Profile update failed:', error);
      throw error;
    }
  }

  /**
   * Logout user (clear FCM token)
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  static async logout(userId) {
    try {
      await prisma.user.update({
        where: { id: userId },
        data: { 
          fcmToken: null,
          updatedAt: new Date()
        }
      });

      logger.info(`User logged out: ${userId}`);

    } catch (error) {
      logger.error('Logout failed:', error);
      throw error;
    }
  }

  /**
   * Deactivate user account
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  static async deactivateUser(userId) {
    try {
      await prisma.user.update({
        where: { id: userId },
        data: { 
          isActive: false,
          fcmToken: null, // Clear FCM token on deactivation
          updatedAt: new Date()
        }
      });

      logger.info(`User deactivated: ${userId}`);

    } catch (error) {
      logger.error('User deactivation failed:', error);
      throw error;
    }
  }

  /**
   * Reactivate user account
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  static async reactivateUser(userId) {
    try {
      await prisma.user.update({
        where: { id: userId },
        data: { 
          isActive: true,
          updatedAt: new Date()
        }
      });

      logger.info(`User reactivated: ${userId}`);

    } catch (error) {
      logger.error('User reactivation failed:', error);
      throw error;
    }
  }

  /**
   * Get user by Firebase UID
   * @param {string} firebaseUid - Firebase UID
   * @returns {Promise<Object|null>} User object or null
   */
  static async getUserByFirebaseUid(firebaseUid) {
    try {
      const user = await prisma.user.findUnique({
        where: { firebaseUid },
        include: {
          ownedCenters: true,
          therapistProfile: {
            include: { center: true }
          },
          patientProfile: {
            include: { center: true }
          }
        }
      });

      return user;

    } catch (error) {
      logger.error('Get user by Firebase UID failed:', error);
      throw error;
    }
  }
}
