import { prisma } from '../config/database.js';
import admin from 'firebase-admin';
import cron from 'node-cron';

/**
 * Enhanced Notification service
 * Handles push notifications, FCM token management, and scheduled notifications
 */
export class NotificationService {
  constructor() {
    this.isInitialized = false;
    this.scheduledJobs = new Map();
  }

  // Initialize the notification service
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Start cron jobs for scheduled notifications
      this.startScheduledNotifications();
      this.isInitialized = true;
      console.log('Notification service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
      throw error;
    }
  }

  // Start scheduled notification jobs
  startScheduledNotifications() {
    // Check for upcoming sessions every 15 minutes
    cron.schedule('*/15 * * * *', async () => {
      await this.sendUpcomingSessionReminders();
    });

    // Send daily session reminders at 8 AM
    cron.schedule('0 8 * * *', async () => {
      await this.sendDailySessionReminders();
    });

    // Send weekly session summaries on Sundays at 6 PM
    cron.schedule('0 18 * * 0', async () => {
      await this.sendWeeklySessionSummaries();
    });

    console.log('Scheduled notification jobs started');
  }

  // Send upcoming session reminders (1 hour before)
  async sendUpcomingSessionReminders() {
    try {
      const now = new Date();
      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
      const reminderWindow = new Date(now.getTime() + 75 * 60 * 1000); // 15-minute window

      const upcomingSessions = await prisma.session.findMany({
        where: {
          status: 'SCHEDULED',
          scheduledAt: {
            gte: oneHourLater,
            lte: reminderWindow
          },
          // Only send if reminder hasn't been sent
          reminderSent: { not: true }
        },
        include: {
          patient: {
            include: {
              user: {
                select: { id: true, displayName: true, fcmTokens: true }
              }
            }
          },
          therapist: {
            include: {
              user: {
                select: { id: true, displayName: true, fcmTokens: true }
              }
            }
          },
          center: {
            select: { name: true, address: true }
          }
        }
      });

      for (const session of upcomingSessions) {
        // Send to patient
        if (session.patient.user) {
          await this.sendNotificationToUser(session.patient.user.id, {
            title: 'Upcoming Appointment',
            body: `Your ${session.type.toLowerCase()} session with ${session.therapist.user.displayName} is in 1 hour`,
            type: 'appointment_reminder',
            data: {
              sessionId: session.id,
              sessionType: session.type,
              therapistName: session.therapist.user.displayName,
              centerName: session.center.name,
              scheduledAt: session.scheduledAt.toISOString()
            }
          });
        }

        // Send to therapist
        await this.sendNotificationToUser(session.therapist.user.id, {
          title: 'Upcoming Session',
          body: `Session with ${session.patient.name} is in 1 hour`,
          type: 'session_reminder',
          data: {
            sessionId: session.id,
            sessionType: session.type,
            patientName: session.patient.name,
            scheduledAt: session.scheduledAt.toISOString()
          }
        });

        // Mark reminder as sent
        await prisma.session.update({
          where: { id: session.id },
          data: { reminderSent: true }
        });
      }

      console.log(`Sent reminders for ${upcomingSessions.length} upcoming sessions`);
    } catch (error) {
      console.error('Error sending upcoming session reminders:', error);
    }
  }

  // Send daily session reminders
  async sendDailySessionReminders() {
    try {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      const todaySessions = await prisma.session.findMany({
        where: {
          status: 'SCHEDULED',
          scheduledAt: {
            gte: startOfDay,
            lt: endOfDay
          }
        },
        include: {
          patient: {
            include: {
              user: {
                select: { id: true, displayName: true, fcmTokens: true }
              }
            }
          },
          therapist: {
            include: {
              user: {
                select: { id: true, displayName: true, fcmTokens: true }
              }
            }
          }
        }
      });

      // Group sessions by user
      const userSessions = new Map();

      todaySessions.forEach(session => {
        // Add to patient's sessions
        if (session.patient.user) {
          if (!userSessions.has(session.patient.user.id)) {
            userSessions.set(session.patient.user.id, {
              user: session.patient.user,
              role: 'patient',
              sessions: []
            });
          }
          userSessions.get(session.patient.user.id).sessions.push(session);
        }

        // Add to therapist's sessions
        if (!userSessions.has(session.therapist.user.id)) {
          userSessions.set(session.therapist.user.id, {
            user: session.therapist.user,
            role: 'therapist',
            sessions: []
          });
        }
        userSessions.get(session.therapist.user.id).sessions.push(session);
      });

      // Send daily reminders
      for (const [userId, userData] of userSessions) {
        const sessionCount = userData.sessions.length;
        const isPatient = userData.role === 'patient';

        await this.sendNotificationToUser(userId, {
          title: 'Today\'s Sessions',
          body: isPatient
            ? `You have ${sessionCount} session${sessionCount > 1 ? 's' : ''} scheduled today`
            : `You have ${sessionCount} session${sessionCount > 1 ? 's' : ''} with patients today`,
          type: 'daily_reminder',
          data: {
            sessionCount: sessionCount.toString(),
            role: userData.role,
            date: today.toISOString().split('T')[0]
          }
        });
      }

      console.log(`Sent daily reminders to ${userSessions.size} users`);
    } catch (error) {
      console.error('Error sending daily session reminders:', error);
    }
  }

  // Send notification to a single user
  async sendNotificationToUser(userId, notification) {
    try {
      // Get user's FCM tokens
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { fcmTokens: true, displayName: true }
      });

      if (!user || !user.fcmTokens || user.fcmTokens.length === 0) {
        console.log(`No FCM tokens found for user ${userId}`);
        return { success: false, reason: 'No FCM tokens' };
      }

      const message = {
        notification: {
          title: notification.title,
          body: notification.body,
          ...(notification.imageUrl && { imageUrl: notification.imageUrl })
        },
        data: {
          type: notification.type || 'general',
          ...(notification.data && notification.data)
        },
        tokens: user.fcmTokens
      };

      // Send multicast message
      const response = await admin.messaging().sendMulticast(message);

      // Handle failed tokens
      if (response.failureCount > 0) {
        const failedTokens = [];
        response.responses.forEach((resp, idx) => {
          if (!resp.success) {
            failedTokens.push(user.fcmTokens[idx]);
            console.error(`Failed to send to token ${user.fcmTokens[idx]}:`, resp.error);
          }
        });

        // Remove invalid tokens
        if (failedTokens.length > 0) {
          await this.removeInvalidTokens(userId, failedTokens);
        }
      }

      // Log notification
      await this.logNotification(userId, notification, response.successCount > 0);

      return {
        success: response.successCount > 0,
        successCount: response.successCount,
        failureCount: response.failureCount
      };

    } catch (error) {
      console.error('Error sending notification:', error);
      await this.logNotification(userId, notification, false, error.message);
      return { success: false, error: error.message };
    }
  }

  // Remove invalid FCM tokens
  async removeInvalidTokens(userId, invalidTokens) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { fcmTokens: true }
      });

      if (user && user.fcmTokens) {
        const validTokens = user.fcmTokens.filter(token => !invalidTokens.includes(token));

        await prisma.user.update({
          where: { id: userId },
          data: { fcmTokens: validTokens }
        });

        console.log(`Removed ${invalidTokens.length} invalid tokens for user ${userId}`);
      }
    } catch (error) {
      console.error('Error removing invalid tokens:', error);
    }
  }

  // Log notification for tracking
  async logNotification(userId, notification, success, error = null) {
    try {
      await prisma.notificationLog.create({
        data: {
          userId,
          title: notification.title,
          body: notification.body,
          type: notification.type || 'general',
          success,
          error,
          sentAt: new Date()
        }
      });
    } catch (logError) {
      console.error('Error logging notification:', logError);
    }
  }

  /**
   * Send push notification to a user
   * @param {string} userId - User ID
   * @param {string} title - Notification title
   * @param {string} body - Notification body
   * @param {Object} data - Additional data (optional)
   * @param {string} type - Notification type
   * @returns {Promise<Object>} Notification result
   */
  static async sendNotificationToUser(userId, title, body, data = {}, type = 'SYSTEM') {
    try {
      // Get user's FCM token
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { fcmToken: true, email: true, isActive: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      if (!user.isActive) {
        throw new Error('User is not active');
      }

      if (!user.fcmToken) {
        logger.warn(`User ${userId} has no FCM token, skipping push notification`);
        // Still create the notification record
        const notification = await this.createNotification(userId, title, body, type);
        return { notification, pushSent: false };
      }

      // Send push notification
      const messageId = await sendPushNotification(user.fcmToken, { title, body }, data);

      // Create notification record
      const notification = await this.createNotification(userId, title, body, type, new Date());
      
      // Mark as sent
      await prisma.notification.update({
        where: { id: notification.id },
        data: { sentAt: new Date() }
      });

      logger.info(`Push notification sent to user ${userId}: ${messageId}`);
      
      return { notification, pushSent: true, messageId };

    } catch (error) {
      logger.error('Failed to send notification to user:', error);
      
      // Still try to create notification record even if push fails
      try {
        const notification = await this.createNotification(userId, title, body, type);
        return { notification, pushSent: false, error: error.message };
      } catch (dbError) {
        logger.error('Failed to create notification record:', dbError);
        throw error;
      }
    }
  }

  /**
   * Send push notification to multiple users
   * @param {string[]} userIds - Array of user IDs
   * @param {string} title - Notification title
   * @param {string} body - Notification body
   * @param {Object} data - Additional data (optional)
   * @param {string} type - Notification type
   * @returns {Promise<Object>} Batch notification result
   */
  static async sendNotificationToUsers(userIds, title, body, data = {}, type = 'SYSTEM') {
    try {
      // Get users with FCM tokens
      const users = await prisma.user.findMany({
        where: {
          id: { in: userIds },
          isActive: true,
          fcmToken: { not: null }
        },
        select: { id: true, fcmToken: true, email: true }
      });

      const fcmTokens = users.map(user => user.fcmToken).filter(Boolean);
      const results = {
        totalUsers: userIds.length,
        usersWithTokens: users.length,
        notifications: [],
        pushResults: null
      };

      // Create notification records for all users
      const notificationPromises = userIds.map(userId =>
        this.createNotification(userId, title, body, type, new Date())
      );
      results.notifications = await Promise.all(notificationPromises);

      // Send push notifications if there are FCM tokens
      if (fcmTokens.length > 0) {
        results.pushResults = await sendMulticastNotification(fcmTokens, { title, body }, data);
        
        // Update sent notifications
        const sentNotificationIds = results.notifications
          .filter((_, index) => users.some(user => user.id === userIds[index]))
          .map(notification => notification.id);

        await prisma.notification.updateMany({
          where: { id: { in: sentNotificationIds } },
          data: { sentAt: new Date() }
        });

        logger.info(`Batch notification sent to ${fcmTokens.length} users: ${results.pushResults.successCount} successful`);
      }

      return results;

    } catch (error) {
      logger.error('Failed to send batch notifications:', error);
      throw error;
    }
  }

  /**
   * Send appointment reminder notifications
   * @param {number} hoursAhead - Hours before appointment to send reminder
   * @returns {Promise<Object>} Reminder results
   */
  static async sendAppointmentReminders(hoursAhead = 2) {
    try {
      const reminderTime = new Date();
      reminderTime.setHours(reminderTime.getHours() + hoursAhead);

      // Get upcoming sessions within the reminder window
      const upcomingSessions = await prisma.session.findMany({
        where: {
          scheduledAt: {
            gte: new Date(),
            lte: reminderTime
          },
          status: 'SCHEDULED'
        },
        include: {
          patient: {
            include: { user: true }
          },
          therapist: {
            include: { user: true }
          },
          center: true
        }
      });

      const reminderResults = {
        totalSessions: upcomingSessions.length,
        patientReminders: 0,
        therapistReminders: 0,
        errors: []
      };

      // Send reminders for each session
      for (const session of upcomingSessions) {
        try {
          const sessionTime = new Date(session.scheduledAt).toLocaleString();
          
          // Send reminder to patient
          if (session.patient.user) {
            await this.sendNotificationToUser(
              session.patient.user.id,
              'Appointment Reminder',
              `You have a ${session.type.toLowerCase()} session at ${sessionTime} at ${session.center.name}`,
              {
                sessionId: session.id,
                type: 'appointment_reminder'
              },
              'REMINDER'
            );
            reminderResults.patientReminders++;
          }

          // Send reminder to therapist
          if (session.therapist.user) {
            await this.sendNotificationToUser(
              session.therapist.user.id,
              'Session Reminder',
              `You have a ${session.type.toLowerCase()} session with ${session.patient.name} at ${sessionTime}`,
              {
                sessionId: session.id,
                type: 'session_reminder'
              },
              'REMINDER'
            );
            reminderResults.therapistReminders++;
          }

        } catch (error) {
          logger.error(`Failed to send reminder for session ${session.id}:`, error);
          reminderResults.errors.push({
            sessionId: session.id,
            error: error.message
          });
        }
      }

      logger.info(`Appointment reminders sent: ${reminderResults.patientReminders} patients, ${reminderResults.therapistReminders} therapists`);
      return reminderResults;

    } catch (error) {
      logger.error('Failed to send appointment reminders:', error);
      throw error;
    }
  }

  /**
   * Get user notifications
   * @param {string} userId - User ID
   * @param {number} limit - Number of notifications to fetch
   * @param {number} offset - Offset for pagination
   * @param {boolean} unreadOnly - Fetch only unread notifications
   * @returns {Promise<Object>} Notifications with pagination info
   */
  static async getUserNotifications(userId, limit = 20, offset = 0, unreadOnly = false) {
    try {
      const where = { userId };
      if (unreadOnly) {
        where.isRead = false;
      }

      const [notifications, total] = await Promise.all([
        prisma.notification.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset
        }),
        prisma.notification.count({ where })
      ]);

      return {
        notifications,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      };

    } catch (error) {
      logger.error('Failed to get user notifications:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   * @param {string} notificationId - Notification ID
   * @param {string} userId - User ID (for authorization)
   * @returns {Promise<Object>} Updated notification
   */
  static async markAsRead(notificationId, userId) {
    try {
      const notification = await prisma.notification.update({
        where: {
          id: notificationId,
          userId: userId // Ensure user can only mark their own notifications
        },
        data: { isRead: true }
      });

      logger.info(`Notification ${notificationId} marked as read by user ${userId}`);
      return notification;

    } catch (error) {
      logger.error('Failed to mark notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param {string} userId - User ID
   * @returns {Promise<number>} Number of notifications updated
   */
  static async markAllAsRead(userId) {
    try {
      const result = await prisma.notification.updateMany({
        where: {
          userId,
          isRead: false
        },
        data: { isRead: true }
      });

      logger.info(`${result.count} notifications marked as read for user ${userId}`);
      return result.count;

    } catch (error) {
      logger.error('Failed to mark all notifications as read:', error);
      throw error;
    }
  }
}
