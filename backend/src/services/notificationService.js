import { prisma } from '../lib/prisma.js';
import { sendPushNotification, sendMulticastNotification } from '../lib/firebase.js';
import { logger } from '../lib/logger.js';

/**
 * Notification service
 * Handles push notifications and FCM token management
 */
export class NotificationService {

  /**
   * Create a notification record in database
   * @param {string} userId - User ID
   * @param {string} title - Notification title
   * @param {string} body - Notification body
   * @param {string} type - Notification type
   * @param {Date} scheduledFor - When to send (optional)
   * @returns {Promise<Object>} Created notification
   */
  static async createNotification(userId, title, body, type = 'SYSTEM', scheduledFor = null) {
    try {
      const notification = await prisma.notification.create({
        data: {
          userId,
          title,
          body,
          type,
          scheduledFor,
          isRead: false
        }
      });

      logger.info(`Notification created for user ${userId}: ${title}`);
      return notification;

    } catch (error) {
      logger.error('Failed to create notification:', error);
      throw error;
    }
  }

  /**
   * Send push notification to a user
   * @param {string} userId - User ID
   * @param {string} title - Notification title
   * @param {string} body - Notification body
   * @param {Object} data - Additional data (optional)
   * @param {string} type - Notification type
   * @returns {Promise<Object>} Notification result
   */
  static async sendNotificationToUser(userId, title, body, data = {}, type = 'SYSTEM') {
    try {
      // Get user's FCM token
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { fcmToken: true, email: true, isActive: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      if (!user.isActive) {
        throw new Error('User is not active');
      }

      if (!user.fcmToken) {
        logger.warn(`User ${userId} has no FCM token, skipping push notification`);
        // Still create the notification record
        const notification = await this.createNotification(userId, title, body, type);
        return { notification, pushSent: false };
      }

      // Send push notification
      const messageId = await sendPushNotification(user.fcmToken, { title, body }, data);

      // Create notification record
      const notification = await this.createNotification(userId, title, body, type, new Date());
      
      // Mark as sent
      await prisma.notification.update({
        where: { id: notification.id },
        data: { sentAt: new Date() }
      });

      logger.info(`Push notification sent to user ${userId}: ${messageId}`);
      
      return { notification, pushSent: true, messageId };

    } catch (error) {
      logger.error('Failed to send notification to user:', error);
      
      // Still try to create notification record even if push fails
      try {
        const notification = await this.createNotification(userId, title, body, type);
        return { notification, pushSent: false, error: error.message };
      } catch (dbError) {
        logger.error('Failed to create notification record:', dbError);
        throw error;
      }
    }
  }

  /**
   * Send push notification to multiple users
   * @param {string[]} userIds - Array of user IDs
   * @param {string} title - Notification title
   * @param {string} body - Notification body
   * @param {Object} data - Additional data (optional)
   * @param {string} type - Notification type
   * @returns {Promise<Object>} Batch notification result
   */
  static async sendNotificationToUsers(userIds, title, body, data = {}, type = 'SYSTEM') {
    try {
      // Get users with FCM tokens
      const users = await prisma.user.findMany({
        where: {
          id: { in: userIds },
          isActive: true,
          fcmToken: { not: null }
        },
        select: { id: true, fcmToken: true, email: true }
      });

      const fcmTokens = users.map(user => user.fcmToken).filter(Boolean);
      const results = {
        totalUsers: userIds.length,
        usersWithTokens: users.length,
        notifications: [],
        pushResults: null
      };

      // Create notification records for all users
      const notificationPromises = userIds.map(userId =>
        this.createNotification(userId, title, body, type, new Date())
      );
      results.notifications = await Promise.all(notificationPromises);

      // Send push notifications if there are FCM tokens
      if (fcmTokens.length > 0) {
        results.pushResults = await sendMulticastNotification(fcmTokens, { title, body }, data);
        
        // Update sent notifications
        const sentNotificationIds = results.notifications
          .filter((_, index) => users.some(user => user.id === userIds[index]))
          .map(notification => notification.id);

        await prisma.notification.updateMany({
          where: { id: { in: sentNotificationIds } },
          data: { sentAt: new Date() }
        });

        logger.info(`Batch notification sent to ${fcmTokens.length} users: ${results.pushResults.successCount} successful`);
      }

      return results;

    } catch (error) {
      logger.error('Failed to send batch notifications:', error);
      throw error;
    }
  }

  /**
   * Send appointment reminder notifications
   * @param {number} hoursAhead - Hours before appointment to send reminder
   * @returns {Promise<Object>} Reminder results
   */
  static async sendAppointmentReminders(hoursAhead = 2) {
    try {
      const reminderTime = new Date();
      reminderTime.setHours(reminderTime.getHours() + hoursAhead);

      // Get upcoming sessions within the reminder window
      const upcomingSessions = await prisma.session.findMany({
        where: {
          scheduledAt: {
            gte: new Date(),
            lte: reminderTime
          },
          status: 'SCHEDULED'
        },
        include: {
          patient: {
            include: { user: true }
          },
          therapist: {
            include: { user: true }
          },
          center: true
        }
      });

      const reminderResults = {
        totalSessions: upcomingSessions.length,
        patientReminders: 0,
        therapistReminders: 0,
        errors: []
      };

      // Send reminders for each session
      for (const session of upcomingSessions) {
        try {
          const sessionTime = new Date(session.scheduledAt).toLocaleString();
          
          // Send reminder to patient
          if (session.patient.user) {
            await this.sendNotificationToUser(
              session.patient.user.id,
              'Appointment Reminder',
              `You have a ${session.type.toLowerCase()} session at ${sessionTime} at ${session.center.name}`,
              {
                sessionId: session.id,
                type: 'appointment_reminder'
              },
              'REMINDER'
            );
            reminderResults.patientReminders++;
          }

          // Send reminder to therapist
          if (session.therapist.user) {
            await this.sendNotificationToUser(
              session.therapist.user.id,
              'Session Reminder',
              `You have a ${session.type.toLowerCase()} session with ${session.patient.name} at ${sessionTime}`,
              {
                sessionId: session.id,
                type: 'session_reminder'
              },
              'REMINDER'
            );
            reminderResults.therapistReminders++;
          }

        } catch (error) {
          logger.error(`Failed to send reminder for session ${session.id}:`, error);
          reminderResults.errors.push({
            sessionId: session.id,
            error: error.message
          });
        }
      }

      logger.info(`Appointment reminders sent: ${reminderResults.patientReminders} patients, ${reminderResults.therapistReminders} therapists`);
      return reminderResults;

    } catch (error) {
      logger.error('Failed to send appointment reminders:', error);
      throw error;
    }
  }

  /**
   * Get user notifications
   * @param {string} userId - User ID
   * @param {number} limit - Number of notifications to fetch
   * @param {number} offset - Offset for pagination
   * @param {boolean} unreadOnly - Fetch only unread notifications
   * @returns {Promise<Object>} Notifications with pagination info
   */
  static async getUserNotifications(userId, limit = 20, offset = 0, unreadOnly = false) {
    try {
      const where = { userId };
      if (unreadOnly) {
        where.isRead = false;
      }

      const [notifications, total] = await Promise.all([
        prisma.notification.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset
        }),
        prisma.notification.count({ where })
      ]);

      return {
        notifications,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      };

    } catch (error) {
      logger.error('Failed to get user notifications:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   * @param {string} notificationId - Notification ID
   * @param {string} userId - User ID (for authorization)
   * @returns {Promise<Object>} Updated notification
   */
  static async markAsRead(notificationId, userId) {
    try {
      const notification = await prisma.notification.update({
        where: {
          id: notificationId,
          userId: userId // Ensure user can only mark their own notifications
        },
        data: { isRead: true }
      });

      logger.info(`Notification ${notificationId} marked as read by user ${userId}`);
      return notification;

    } catch (error) {
      logger.error('Failed to mark notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param {string} userId - User ID
   * @returns {Promise<number>} Number of notifications updated
   */
  static async markAllAsRead(userId) {
    try {
      const result = await prisma.notification.updateMany({
        where: {
          userId,
          isRead: false
        },
        data: { isRead: true }
      });

      logger.info(`${result.count} notifications marked as read for user ${userId}`);
      return result.count;

    } catch (error) {
      logger.error('Failed to mark all notifications as read:', error);
      throw error;
    }
  }
}
