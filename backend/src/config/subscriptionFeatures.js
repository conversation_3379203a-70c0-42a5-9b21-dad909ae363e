/**
 * Subscription plan features and limitations
 */

export const SUBSCRIPTION_FEATURES = {
  TRIAL: {
    name: 'Trial',
    duration: 30, // days
    price: 0,
    currency: 'BDT',
    features: {
      // Core Features
      maxBranches: 1,
      maxPatients: 10,
      maxTherapists: 2,
      maxEmployees: 3,
      maxSessions: 50,
      
      // Module Access
      patientManagement: true,
      sessionManagement: true,
      basicReports: true,
      notifications: true,
      
      // Advanced Features
      multiBranch: false,
      hrmModule: false,
      accountingModule: false,
      advancedReports: false,
      apiAccess: false,
      customBranding: false,
      prioritySupport: false,
      dataExport: false,
      
      // Storage & Limits
      fileStorageGB: 1,
      monthlyEmailNotifications: 100,
      monthlySMSNotifications: 0,
    }
  },
  
  BASIC: {
    name: 'Basic',
    duration: null, // unlimited
    price: 1500,
    currency: 'BDT',
    features: {
      // Core Features
      maxBranches: 1,
      maxPatients: 100,
      maxTherapists: 5,
      maxEmployees: 10,
      maxSessions: 500,
      
      // Module Access
      patientManagement: true,
      sessionManagement: true,
      basicReports: true,
      notifications: true,
      
      // Advanced Features
      multiBranch: false,
      hrmModule: true,
      accountingModule: false,
      advancedReports: true,
      apiAccess: false,
      customBranding: false,
      prioritySupport: false,
      dataExport: true,
      
      // Storage & Limits
      fileStorageGB: 5,
      monthlyEmailNotifications: 500,
      monthlySMSNotifications: 100,
    }
  },
  
  PRO: {
    name: 'Pro',
    duration: null, // unlimited
    price: 3000,
    currency: 'BDT',
    features: {
      // Core Features
      maxBranches: 5,
      maxPatients: 500,
      maxTherapists: 20,
      maxEmployees: 50,
      maxSessions: 2000,
      
      // Module Access
      patientManagement: true,
      sessionManagement: true,
      basicReports: true,
      notifications: true,
      
      // Advanced Features
      multiBranch: true,
      hrmModule: true,
      accountingModule: true,
      advancedReports: true,
      apiAccess: true,
      customBranding: true,
      prioritySupport: true,
      dataExport: true,
      
      // Storage & Limits
      fileStorageGB: 20,
      monthlyEmailNotifications: 2000,
      monthlySMSNotifications: 500,
    }
  },
  
  ENTERPRISE: {
    name: 'Enterprise',
    duration: null, // unlimited
    price: 5000,
    currency: 'BDT',
    features: {
      // Core Features
      maxBranches: -1, // unlimited
      maxPatients: -1, // unlimited
      maxTherapists: -1, // unlimited
      maxEmployees: -1, // unlimited
      maxSessions: -1, // unlimited
      
      // Module Access
      patientManagement: true,
      sessionManagement: true,
      basicReports: true,
      notifications: true,
      
      // Advanced Features
      multiBranch: true,
      hrmModule: true,
      accountingModule: true,
      advancedReports: true,
      apiAccess: true,
      customBranding: true,
      prioritySupport: true,
      dataExport: true,
      
      // Storage & Limits
      fileStorageGB: -1, // unlimited
      monthlyEmailNotifications: -1, // unlimited
      monthlySMSNotifications: -1, // unlimited
    }
  }
};

/**
 * Get features for a subscription plan
 * @param {string} plan - Subscription plan name
 * @returns {Object} Plan features
 */
export function getPlanFeatures(plan) {
  return SUBSCRIPTION_FEATURES[plan] || SUBSCRIPTION_FEATURES.TRIAL;
}

/**
 * Check if a feature is available for a plan
 * @param {string} plan - Subscription plan name
 * @param {string} feature - Feature name
 * @returns {boolean} Whether feature is available
 */
export function hasFeature(plan, feature) {
  const planFeatures = getPlanFeatures(plan);
  return planFeatures.features[feature] === true;
}

/**
 * Get limit for a feature
 * @param {string} plan - Subscription plan name
 * @param {string} feature - Feature name
 * @returns {number} Feature limit (-1 for unlimited, 0 for disabled)
 */
export function getFeatureLimit(plan, feature) {
  const planFeatures = getPlanFeatures(plan);
  return planFeatures.features[feature] || 0;
}

/**
 * Check if limit is exceeded
 * @param {string} plan - Subscription plan name
 * @param {string} feature - Feature name
 * @param {number} currentCount - Current usage count
 * @returns {boolean} Whether limit is exceeded
 */
export function isLimitExceeded(plan, feature, currentCount) {
  const limit = getFeatureLimit(plan, feature);
  
  // -1 means unlimited
  if (limit === -1) return false;
  
  // 0 means disabled
  if (limit === 0) return true;
  
  return currentCount >= limit;
}

/**
 * Get all available plans for display
 * @returns {Array} Array of plan objects
 */
export function getAllPlans() {
  return Object.entries(SUBSCRIPTION_FEATURES).map(([key, plan]) => ({
    id: key,
    ...plan
  }));
}

/**
 * Get plan comparison data
 * @returns {Object} Comparison data for all plans
 */
export function getPlanComparison() {
  const plans = getAllPlans();
  const features = [
    'maxBranches',
    'maxPatients',
    'maxTherapists',
    'maxEmployees',
    'multiBranch',
    'hrmModule',
    'accountingModule',
    'advancedReports',
    'apiAccess',
    'customBranding',
    'prioritySupport',
    'fileStorageGB'
  ];
  
  return {
    plans,
    features,
    comparison: plans.reduce((acc, plan) => {
      acc[plan.id] = features.reduce((featureAcc, feature) => {
        featureAcc[feature] = plan.features[feature];
        return featureAcc;
      }, {});
      return acc;
    }, {})
  };
}

/**
 * Calculate subscription price with discounts
 * @param {string} plan - Subscription plan name
 * @param {string} billing - Billing cycle (monthly/yearly)
 * @param {number} discount - Discount percentage
 * @returns {Object} Price calculation
 */
export function calculatePrice(plan, billing = 'monthly', discount = 0) {
  const planData = getPlanFeatures(plan);
  let basePrice = planData.price;
  
  if (billing === 'yearly') {
    basePrice = basePrice * 12;
    // Apply yearly discount (default 20%)
    const yearlyDiscount = discount || 20;
    basePrice = basePrice * (1 - yearlyDiscount / 100);
  }
  
  const discountAmount = basePrice * (discount / 100);
  const finalPrice = basePrice - discountAmount;
  
  return {
    basePrice: planData.price,
    billing,
    discount,
    discountAmount,
    finalPrice,
    currency: planData.currency,
    savings: billing === 'yearly' ? (planData.price * 12) - finalPrice : 0
  };
}
