import { verifyIdToken } from '../lib/firebase.js';
import { prisma } from '../lib/prisma.js';
import { logger } from '../lib/logger.js';

/**
 * Authentication plugin for Fastify
 * Provides Firebase authentication and user context
 */
export async function authPlugin(fastify, options) {
  // Register authentication decorator
  fastify.decorate('authenticate', async function(request, reply) {
    try {
      const authHeader = request.headers.authorization;
      
      if (!authHeader) {
        throw new Error('Authorization header is required');
      }

      if (!authHeader.startsWith('Bearer ')) {
        throw new Error('Authorization header must start with Bear<PERSON>');
      }

      const idToken = authHeader.substring(7); // Remove 'Bearer ' prefix
      
      if (!idToken) {
        throw new Error('Authorization token is required');
      }

      // Verify Firebase ID token
      const decodedToken = await verifyIdToken(idToken);
      
      if (!decodedToken) {
        throw new Error('Invalid authorization token');
      }

      // Get user from database
      const user = await prisma.user.findUnique({
        where: { firebaseUid: decodedToken.uid },
        include: {
          ownedCenters: true,
          therapistProfile: {
            include: {
              center: true
            }
          },
          patientProfile: {
            include: {
              center: true
            }
          }
        }
      });

      if (!user) {
        throw new Error('User not found in database');
      }

      if (!user.isActive) {
        throw new Error('User account is deactivated');
      }

      // Add user context to request
      request.user = user;
      request.firebaseToken = decodedToken;

    } catch (error) {
      logger.error('Authentication failed:', error);
      
      // Determine appropriate error response
      let statusCode = 401;
      let message = 'Authentication failed';

      if (error.message.includes('expired')) {
        message = 'Authentication token has expired';
      } else if (error.message.includes('invalid')) {
        message = 'Invalid authentication token';
      } else if (error.message.includes('not found')) {
        message = 'User not found';
        statusCode = 404;
      } else if (error.message.includes('deactivated')) {
        message = 'User account is deactivated';
        statusCode = 403;
      }

      reply.code(statusCode).send({
        error: 'Authentication Error',
        message,
        statusCode,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Register role-based authorization decorator
  fastify.decorate('authorize', function(allowedRoles = []) {
    return async function(request, reply) {
      try {
        // Ensure user is authenticated first
        if (!request.user) {
          throw new Error('User not authenticated');
        }

        // Check if user role is allowed
        if (allowedRoles.length > 0 && !allowedRoles.includes(request.user.role)) {
          throw new Error('Insufficient permissions');
        }

        // Additional role-specific checks
        switch (request.user.role) {
          case 'THERAPIST':
            // Therapists can only access their own center's data
            if (request.params.centerId && request.user.therapistProfile?.centerId !== request.params.centerId) {
              throw new Error('Access denied to this center');
            }
            break;
          
          case 'PATIENT':
            // Patients can only access their own data
            if (request.params.patientId && request.user.patientProfile?.id !== request.params.patientId) {
              throw new Error('Access denied to this patient data');
            }
            break;
          
          case 'CENTER_OWNER':
            // Center owners can only access their own centers
            if (request.params.centerId) {
              const ownsCenter = request.user.ownedCenters.some(center => center.id === request.params.centerId);
              if (!ownsCenter) {
                throw new Error('Access denied to this center');
              }
            }
            break;
          
          case 'SUPER_ADMIN':
            // Super admins have access to everything
            break;
          
          default:
            throw new Error('Invalid user role');
        }

      } catch (error) {
        logger.error('Authorization failed:', error);
        
        let statusCode = 403;
        let message = 'Access denied';

        if (error.message.includes('not authenticated')) {
          statusCode = 401;
          message = 'Authentication required';
        } else if (error.message.includes('Insufficient permissions')) {
          message = 'Insufficient permissions for this operation';
        } else if (error.message.includes('Access denied')) {
          message = error.message;
        }

        reply.code(statusCode).send({
          error: 'Authorization Error',
          message,
          statusCode,
          timestamp: new Date().toISOString()
        });
      }
    };
  });

  // Register subscription check decorator
  fastify.decorate('checkSubscription', function(requiredPlan = null) {
    return async function(request, reply) {
      try {
        if (!request.user) {
          throw new Error('User not authenticated');
        }

        // Get user's center (for center owners and therapists)
        let userCenter = null;
        
        if (request.user.role === 'CENTER_OWNER') {
          // For center owners, check their primary center or the one being accessed
          if (request.params.centerId) {
            userCenter = request.user.ownedCenters.find(center => center.id === request.params.centerId);
          } else {
            userCenter = request.user.ownedCenters[0]; // Primary center
          }
        } else if (request.user.role === 'THERAPIST') {
          userCenter = request.user.therapistProfile?.center;
        }

        if (!userCenter) {
          throw new Error('No associated center found');
        }

        // Check if subscription is active
        const now = new Date();
        if (userCenter.subscriptionEnd && userCenter.subscriptionEnd < now) {
          throw new Error('Subscription has expired');
        }

        // Check if required plan is met
        if (requiredPlan) {
          const planHierarchy = { TRIAL: 1, BASIC: 2, PRO: 3 };
          const userPlanLevel = planHierarchy[userCenter.subscriptionPlan];
          const requiredPlanLevel = planHierarchy[requiredPlan];

          if (userPlanLevel < requiredPlanLevel) {
            throw new Error(`This feature requires ${requiredPlan} subscription`);
          }
        }

        // Add center context to request
        request.userCenter = userCenter;

      } catch (error) {
        logger.error('Subscription check failed:', error);
        
        let statusCode = 403;
        let message = 'Subscription required';

        if (error.message.includes('expired')) {
          message = 'Your subscription has expired. Please renew to continue.';
        } else if (error.message.includes('requires')) {
          message = error.message;
          statusCode = 402; // Payment Required
        } else if (error.message.includes('No associated center')) {
          message = 'No associated center found';
          statusCode = 404;
        }

        reply.code(statusCode).send({
          error: 'Subscription Error',
          message,
          statusCode,
          timestamp: new Date().toISOString()
        });
      }
    };
  });
}
