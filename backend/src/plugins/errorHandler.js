import { logger } from '../lib/logger.js';

/**
 * Error handler plugin for Fastify
 * Provides consistent error responses and logging
 */
export async function errorHandler(fastify, options) {
  // Set error handler
  fastify.setErrorHandler(async (error, request, reply) => {
    const { method, url } = request;
    const userAgent = request.headers['user-agent'];
    const ip = request.ip;

    // Log the error with context
    logger.logError(error, {
      method,
      url,
      userAgent,
      ip,
      body: request.body,
      params: request.params,
      query: request.query
    });

    // Handle different types of errors
    if (error.validation) {
      // Validation errors
      reply.code(400).send({
        error: 'Validation Error',
        message: 'Invalid request data',
        details: error.validation.map(err => ({
          field: err.instancePath || err.schemaPath,
          message: err.message,
          value: err.data
        })),
        statusCode: 400,
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (error.code === 'FST_JWT_NO_AUTHORIZATION_IN_HEADER') {
      // JWT missing
      reply.code(401).send({
        error: 'Unauthorized',
        message: 'Authorization token is required',
        statusCode: 401,
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (error.code === 'FST_JWT_AUTHORIZATION_TOKEN_INVALID') {
      // JWT invalid
      reply.code(401).send({
        error: 'Unauthorized',
        message: 'Invalid authorization token',
        statusCode: 401,
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (error.statusCode === 429) {
      // Rate limit exceeded
      reply.code(429).send({
        error: 'Too Many Requests',
        message: 'Rate limit exceeded. Please try again later.',
        statusCode: 429,
        timestamp: new Date().toISOString(),
        retryAfter: error.retryAfter
      });
      return;
    }

    // Prisma errors
    if (error.code?.startsWith('P')) {
      let message = 'Database operation failed';
      let statusCode = 500;

      switch (error.code) {
        case 'P2002':
          message = 'A record with this information already exists';
          statusCode = 409;
          break;
        case 'P2025':
          message = 'Record not found';
          statusCode = 404;
          break;
        case 'P2003':
          message = 'Invalid reference to related record';
          statusCode = 400;
          break;
        case 'P2014':
          message = 'Invalid ID provided';
          statusCode = 400;
          break;
        default:
          // Keep default message and status code
          break;
      }

      reply.code(statusCode).send({
        error: 'Database Error',
        message,
        statusCode,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Firebase errors
    if (error.code?.startsWith('auth/')) {
      let message = 'Authentication failed';
      let statusCode = 401;

      switch (error.code) {
        case 'auth/id-token-expired':
          message = 'Authentication token has expired';
          break;
        case 'auth/id-token-revoked':
          message = 'Authentication token has been revoked';
          break;
        case 'auth/invalid-id-token':
          message = 'Invalid authentication token';
          break;
        case 'auth/user-not-found':
          message = 'User not found';
          statusCode = 404;
          break;
        default:
          // Keep default message and status code
          break;
      }

      reply.code(statusCode).send({
        error: 'Authentication Error',
        message,
        statusCode,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Handle known HTTP status codes
    if (error.statusCode) {
      const statusCode = error.statusCode;
      let errorType = 'Error';

      switch (statusCode) {
        case 400:
          errorType = 'Bad Request';
          break;
        case 401:
          errorType = 'Unauthorized';
          break;
        case 403:
          errorType = 'Forbidden';
          break;
        case 404:
          errorType = 'Not Found';
          break;
        case 409:
          errorType = 'Conflict';
          break;
        case 422:
          errorType = 'Unprocessable Entity';
          break;
        case 500:
          errorType = 'Internal Server Error';
          break;
        default:
          errorType = 'Error';
          break;
      }

      reply.code(statusCode).send({
        error: errorType,
        message: error.message || 'An error occurred',
        statusCode,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Default error response for unhandled errors
    const statusCode = 500;
    const message = process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : error.message;

    reply.code(statusCode).send({
      error: 'Internal Server Error',
      message,
      statusCode,
      timestamp: new Date().toISOString(),
      ...(process.env.NODE_ENV !== 'production' && { stack: error.stack })
    });
  });

  // Add not found handler
  fastify.setNotFoundHandler(async (request, reply) => {
    reply.code(404).send({
      error: 'Not Found',
      message: `Route ${request.method} ${request.url} not found`,
      statusCode: 404,
      timestamp: new Date().toISOString()
    });
  });

  // Add request logging hook
  fastify.addHook('onResponse', async (request, reply) => {
    const responseTime = reply.getResponseTime();
    logger.logRequest(request, reply, responseTime);
  });
}
