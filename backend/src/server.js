import Fastify from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import rateLimit from '@fastify/rate-limit';
import multipart from '@fastify/multipart';
import staticFiles from '@fastify/static';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import jwt from '@fastify/jwt';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import utilities and plugins
import { prisma } from './lib/prisma.js';
import { logger } from './lib/logger.js';
import { initializeFirebase } from './lib/firebase.js';
import { errorHandler } from './plugins/errorHandler.js';
import { authPlugin } from './plugins/auth.js';

// Import routes
import authRoutes from './routes/auth.js';
import adminRoutes from './routes/admin.js';
import centerRoutes from './routes/center.js';
import patientRoutes from './routes/patient.js';
import sessionRoutes from './routes/session.js';
import notificationRoutes from './routes/notification.js';
import subscriptionRoutes from './routes/subscription.js';

// Import HRM routes
import employeeRoutes from './routes/hrm/employee.js';

// Import Accounting routes
import expenseRoutes from './routes/accounting/expense.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create Fastify instance
const fastify = Fastify({
  logger: {
    level: process.env.LOG_LEVEL || 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true
      }
    }
  }
});

// Register plugins
async function registerPlugins() {
  // Security plugins
  await fastify.register(helmet, {
    contentSecurityPolicy: false
  });

  await fastify.register(cors, {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000', 'http://localhost:5173'],
    credentials: true
  });

  await fastify.register(rateLimit, {
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    timeWindow: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000 // 15 minutes
  });

  // File upload support
  await fastify.register(multipart, {
    limits: {
      fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024 // 5MB
    }
  });

  // Static files
  await fastify.register(staticFiles, {
    root: join(__dirname, '../uploads'),
    prefix: '/uploads/'
  });

  // API Documentation
  if (process.env.ENABLE_API_DOCS === 'true') {
    await fastify.register(swagger, {
      swagger: {
        info: {
          title: 'PhysioCenter API',
          description: 'API documentation for PhysioCenter SaaS application',
          version: '1.0.0'
        },
        host: `${process.env.HOST || 'localhost'}:${process.env.PORT || 3000}`,
        schemes: ['http', 'https'],
        consumes: ['application/json'],
        produces: ['application/json'],
        securityDefinitions: {
          Bearer: {
            type: 'apiKey',
            name: 'Authorization',
            in: 'header'
          }
        }
      }
    });

    await fastify.register(swaggerUi, {
      routePrefix: '/docs',
      uiConfig: {
        docExpansion: 'full',
        deepLinking: false
      }
    });
  }

  // JWT plugin
  await fastify.register(jwt, {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key'
  });

  // Custom plugins
  await fastify.register(errorHandler);
  await fastify.register(authPlugin);
}

// Register routes
async function registerRoutes() {
  // Health check
  fastify.get('/health', async (request, reply) => {
    try {
      // Check database connection
      await prisma.$queryRaw`SELECT 1`;
      
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: '1.0.0'
      };
    } catch (error) {
      reply.code(503);
      return {
        status: 'error',
        message: 'Database connection failed',
        timestamp: new Date().toISOString()
      };
    }
  });

  // API routes
  await fastify.register(authRoutes, { prefix: '/api/auth' });
  await fastify.register(adminRoutes, { prefix: '/api/admin' });
  await fastify.register(centerRoutes, { prefix: '/api/centers' });
  await fastify.register(patientRoutes, { prefix: '/api/patients' });
  await fastify.register(sessionRoutes, { prefix: '/api/sessions' });
  await fastify.register(notificationRoutes, { prefix: '/api/notifications' });
  await fastify.register(subscriptionRoutes, { prefix: '/api/subscriptions' });

  // HRM routes
  await fastify.register(employeeRoutes, { prefix: '/api/hrm/employees' });

  // Accounting routes
  await fastify.register(expenseRoutes, { prefix: '/api/accounting/expenses' });
}

// Start server
async function start() {
  try {
    // Initialize Firebase
    await initializeFirebase();
    logger.info('Firebase initialized successfully');

    // Register plugins and routes
    await registerPlugins();
    await registerRoutes();

    // Start server
    const port = parseInt(process.env.PORT) || 3000;
    const host = process.env.HOST || 'localhost';

    await fastify.listen({ port, host });
    
    logger.info(`Server running on http://${host}:${port}`);
    if (process.env.ENABLE_API_DOCS === 'true') {
      logger.info(`API Documentation available at http://${host}:${port}/docs`);
    }
  } catch (error) {
    logger.error('Error starting server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down gracefully...');
  try {
    await fastify.close();
    await prisma.$disconnect();
    logger.info('Server closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down gracefully...');
  try {
    await fastify.close();
    await prisma.$disconnect();
    logger.info('Server closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

// Start the server
start();
