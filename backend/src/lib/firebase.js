import admin from 'firebase-admin';
import { logger } from './logger.js';

let firebaseApp = null;

/**
 * Initialize Firebase Admin SDK
 */
export async function initializeFirebase() {
  try {
    // Check if Firebase is already initialized
    if (firebaseApp) {
      return firebaseApp;
    }

    // Validate required environment variables
    const requiredEnvVars = [
      'FIREBASE_PROJECT_ID',
      'FIREBASE_PRIVATE_KEY',
      'FIREBASE_CLIENT_EMAIL'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    if (missingVars.length > 0) {
      throw new Error(`Missing required Firebase environment variables: ${missingVars.join(', ')}`);
    }

    // Parse the private key (handle escaped newlines)
    const privateKey = process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n');

    // Initialize Firebase Admin
    firebaseApp = admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        privateKey: privateKey,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      }),
      projectId: process.env.FIREBASE_PROJECT_ID,
    });

    logger.info('Firebase Admin SDK initialized successfully');
    return firebaseApp;
  } catch (error) {
    logger.error('Failed to initialize Firebase Admin SDK:', error);
    throw error;
  }
}

/**
 * Verify Firebase ID token
 * @param {string} idToken - Firebase ID token
 * @returns {Promise<admin.auth.DecodedIdToken>} Decoded token
 */
export async function verifyIdToken(idToken) {
  try {
    if (!firebaseApp) {
      throw new Error('Firebase not initialized');
    }

    const decodedToken = await admin.auth().verifyIdToken(idToken);
    return decodedToken;
  } catch (error) {
    logger.error('Failed to verify Firebase ID token:', error);
    throw error;
  }
}

/**
 * Get user by Firebase UID
 * @param {string} uid - Firebase UID
 * @returns {Promise<admin.auth.UserRecord>} User record
 */
export async function getFirebaseUser(uid) {
  try {
    if (!firebaseApp) {
      throw new Error('Firebase not initialized');
    }

    const userRecord = await admin.auth().getUser(uid);
    return userRecord;
  } catch (error) {
    logger.error('Failed to get Firebase user:', error);
    throw error;
  }
}

/**
 * Send push notification using FCM
 * @param {string} fcmToken - FCM token
 * @param {Object} notification - Notification payload
 * @param {Object} data - Data payload (optional)
 * @returns {Promise<string>} Message ID
 */
export async function sendPushNotification(fcmToken, notification, data = {}) {
  try {
    if (!firebaseApp) {
      throw new Error('Firebase not initialized');
    }

    const message = {
      token: fcmToken,
      notification: {
        title: notification.title,
        body: notification.body,
      },
      data: {
        ...data,
        timestamp: new Date().toISOString(),
      },
      android: {
        notification: {
          icon: 'ic_notification',
          color: '#2563eb',
          sound: 'default',
        },
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1,
          },
        },
      },
    };

    const response = await admin.messaging().send(message);
    logger.info(`Push notification sent successfully: ${response}`);
    return response;
  } catch (error) {
    logger.error('Failed to send push notification:', error);
    throw error;
  }
}

/**
 * Send push notification to multiple tokens
 * @param {string[]} fcmTokens - Array of FCM tokens
 * @param {Object} notification - Notification payload
 * @param {Object} data - Data payload (optional)
 * @returns {Promise<admin.messaging.BatchResponse>} Batch response
 */
export async function sendMulticastNotification(fcmTokens, notification, data = {}) {
  try {
    if (!firebaseApp) {
      throw new Error('Firebase not initialized');
    }

    if (!fcmTokens || fcmTokens.length === 0) {
      throw new Error('No FCM tokens provided');
    }

    const message = {
      tokens: fcmTokens,
      notification: {
        title: notification.title,
        body: notification.body,
      },
      data: {
        ...data,
        timestamp: new Date().toISOString(),
      },
      android: {
        notification: {
          icon: 'ic_notification',
          color: '#2563eb',
          sound: 'default',
        },
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1,
          },
        },
      },
    };

    const response = await admin.messaging().sendMulticast(message);
    logger.info(`Multicast notification sent: ${response.successCount}/${fcmTokens.length} successful`);
    
    if (response.failureCount > 0) {
      response.responses.forEach((resp, idx) => {
        if (!resp.success) {
          logger.error(`Failed to send to token ${fcmTokens[idx]}:`, resp.error);
        }
      });
    }

    return response;
  } catch (error) {
    logger.error('Failed to send multicast notification:', error);
    throw error;
  }
}

/**
 * Create custom token for user
 * @param {string} uid - User UID
 * @param {Object} additionalClaims - Additional claims (optional)
 * @returns {Promise<string>} Custom token
 */
export async function createCustomToken(uid, additionalClaims = {}) {
  try {
    if (!firebaseApp) {
      throw new Error('Firebase not initialized');
    }

    const customToken = await admin.auth().createCustomToken(uid, additionalClaims);
    return customToken;
  } catch (error) {
    logger.error('Failed to create custom token:', error);
    throw error;
  }
}

export { admin };
