// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String   // Hashed password
  name      String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admins")
}

model User {
  id                String   @id @default(cuid())
  firebaseUid       String   @unique
  email             String   @unique
  displayName       String?
  photoURL          String?
  fcmToken          String?
  role              UserRole @default(CENTER_OWNER)
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  ownedCenters      Center[]
  therapistProfile  Therapist?
  patientProfile    Patient?
  employeeProfile   Employee?
  notifications     Notification[]

  @@map("users")
}

model Center {
  id                String            @id @default(cuid())
  name              String
  address           String
  phone             String?
  email             String?
  licenseNumber     String?
  licenseExpiry     DateTime?
  subscriptionPlan  SubscriptionPlan  @default(TRIAL)
  subscriptionStart DateTime          @default(now())
  subscriptionEnd   DateTime?
  isActive          Boolean           @default(true)
  isMainBranch      Boolean           @default(true)
  parentCenterId    String?           // For multi-branch support
  branchCode        String?           // Unique branch identifier
  maxBranches       Int               @default(1)
  maxPatients       Int               @default(50)
  maxTherapists     Int               @default(5)
  maxEmployees      Int               @default(10)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  owner             User              @relation(fields: [ownerId], references: [id])
  ownerId           String
  parentCenter      Center?           @relation("CenterBranches", fields: [parentCenterId], references: [id])
  branches          Center[]          @relation("CenterBranches")
  therapists        Therapist[]
  patients          Patient[]
  sessions          Session[]
  employees         Employee[]
  expenses          Expense[]
  purchases         Purchase[]
  invoices          Invoice[]
  payments          Payment[]

  @@map("centers")
}

model Therapist {
  id                String   @id @default(cuid())
  specialization    String?
  licenseNumber     String?
  experience        Int?     // years of experience
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user              User     @relation(fields: [userId], references: [id])
  userId            String   @unique
  center            Center   @relation(fields: [centerId], references: [id])
  centerId          String
  sessions          Session[]

  @@map("therapists")
}

model Patient {
  id                String   @id @default(cuid())
  name              String
  age               Int?
  phone             String?
  address           String?
  medicalHistory    String?  @db.Text
  profilePhoto      String?
  emergencyContact  String?
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user              User?    @relation(fields: [userId], references: [id])
  userId            String?  @unique
  center            Center   @relation(fields: [centerId], references: [id])
  centerId          String
  sessions          Session[]

  @@map("patients")
}

model Session {
  id                String        @id @default(cuid())
  type              SessionType   @default(THERAPY)
  scheduledAt       DateTime
  duration          Int           // minutes
  status            SessionStatus @default(SCHEDULED)
  notes             String?       @db.Text
  painScoreBefore   Int?          // 1-10 scale
  painScoreAfter    Int?          // 1-10 scale
  progressNotes     String?       @db.Text
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  center            Center        @relation(fields: [centerId], references: [id])
  centerId          String
  patient           Patient       @relation(fields: [patientId], references: [id])
  patientId         String
  therapist         Therapist     @relation(fields: [therapistId], references: [id])
  therapistId       String

  @@map("sessions")
}

model Notification {
  id                String             @id @default(cuid())
  title             String
  body              String
  type              NotificationType   @default(REMINDER)
  isRead            Boolean            @default(false)
  scheduledFor      DateTime?
  sentAt            DateTime?
  createdAt         DateTime           @default(now())

  // Relations
  user              User               @relation(fields: [userId], references: [id])
  userId            String

  @@map("notifications")
}

enum UserRole {
  SUPER_ADMIN
  CENTER_OWNER
  THERAPIST
  PATIENT
}

enum SubscriptionPlan {
  TRIAL
  BASIC
  PRO
  ENTERPRISE
}

enum SessionType {
  CONSULTATION
  THERAPY
  FOLLOW_UP
}

enum SessionStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum NotificationType {
  REMINDER
  APPOINTMENT
  PAYMENT
  SYSTEM
}

// HRM Enums
enum EmploymentType {
  FULL_TIME
  PART_TIME
  CONTRACT
  INTERN
}

enum EmployeeStatus {
  ACTIVE
  INACTIVE
  TERMINATED
  ON_LEAVE
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  HALF_DAY
  SICK_LEAVE
  CASUAL_LEAVE
  HOLIDAY
}

enum LeaveType {
  SICK_LEAVE
  CASUAL_LEAVE
  ANNUAL_LEAVE
  MATERNITY_LEAVE
  PATERNITY_LEAVE
  EMERGENCY_LEAVE
  UNPAID_LEAVE
}

enum LeaveStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

enum PayrollStatus {
  PENDING
  APPROVED
  PAID
  CANCELLED
}

// Accounting Enums
enum ExpenseCategory {
  OFFICE_SUPPLIES
  EQUIPMENT
  UTILITIES
  RENT
  MARKETING
  TRAVEL
  MEALS
  PROFESSIONAL_SERVICES
  INSURANCE
  MAINTENANCE
  OTHER
}

enum ExpenseStatus {
  PENDING
  APPROVED
  REJECTED
  PAID
}

enum PaymentMethod {
  CASH
  BANK_TRANSFER
  CREDIT_CARD
  DEBIT_CARD
  MOBILE_BANKING
  CHECK
  OTHER
}

enum PurchaseStatus {
  PENDING
  APPROVED
  ORDERED
  PARTIALLY_RECEIVED
  RECEIVED
  CANCELLED
}

enum InvoiceStatus {
  DRAFT
  SENT
  VIEWED
  PARTIALLY_PAID
  PAID
  OVERDUE
  CANCELLED
}

// HRM Models
model Employee {
  id                String            @id @default(cuid())
  employeeId        String            @unique
  firstName         String
  lastName          String
  email             String?
  phone             String?
  address           String?
  dateOfBirth       DateTime?
  hireDate          DateTime          @default(now())
  terminationDate   DateTime?
  position          String
  department        String?
  salary            Decimal?          @db.Decimal(10, 2)
  hourlyRate        Decimal?          @db.Decimal(8, 2)
  employmentType    EmploymentType    @default(FULL_TIME)
  status            EmployeeStatus    @default(ACTIVE)
  emergencyContact  String?
  emergencyPhone    String?
  profilePhoto      String?
  notes             String?           @db.Text
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  center            Center            @relation(fields: [centerId], references: [id])
  centerId          String
  user              User?             @relation(fields: [userId], references: [id])
  userId            String?           @unique
  attendanceRecords Attendance[]
  leaveRequests     LeaveRequest[]
  payrollRecords    PayrollRecord[]

  @@map("employees")
}

model Attendance {
  id                String            @id @default(cuid())
  date              DateTime          @db.Date
  checkIn           DateTime?
  checkOut          DateTime?
  breakStart        DateTime?
  breakEnd          DateTime?
  totalHours        Decimal?          @db.Decimal(4, 2)
  overtimeHours     Decimal?          @db.Decimal(4, 2)
  status            AttendanceStatus  @default(PRESENT)
  notes             String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  employee          Employee          @relation(fields: [employeeId], references: [id])
  employeeId        String

  @@unique([employeeId, date])
  @@map("attendance")
}

model LeaveRequest {
  id                String            @id @default(cuid())
  leaveType         LeaveType
  startDate         DateTime          @db.Date
  endDate           DateTime          @db.Date
  totalDays         Int
  reason            String            @db.Text
  status            LeaveStatus       @default(PENDING)
  appliedAt         DateTime          @default(now())
  approvedAt        DateTime?
  approvedBy        String?
  rejectedAt        DateTime?
  rejectionReason   String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  employee          Employee          @relation(fields: [employeeId], references: [id])
  employeeId        String

  @@map("leave_requests")
}

model PayrollRecord {
  id                String            @id @default(cuid())
  payPeriodStart    DateTime          @db.Date
  payPeriodEnd      DateTime          @db.Date
  basicSalary       Decimal           @db.Decimal(10, 2)
  overtimePay       Decimal           @default(0) @db.Decimal(10, 2)
  bonuses           Decimal           @default(0) @db.Decimal(10, 2)
  deductions        Decimal           @default(0) @db.Decimal(10, 2)
  grossPay          Decimal           @db.Decimal(10, 2)
  netPay            Decimal           @db.Decimal(10, 2)
  taxDeduction      Decimal           @default(0) @db.Decimal(10, 2)
  providentFund     Decimal           @default(0) @db.Decimal(10, 2)
  status            PayrollStatus     @default(PENDING)
  paidAt            DateTime?
  notes             String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  employee          Employee          @relation(fields: [employeeId], references: [id])
  employeeId        String

  @@map("payroll_records")
}

// Accounting Models
model Expense {
  id                String            @id @default(cuid())
  expenseNumber     String            @unique
  title             String
  description       String?           @db.Text
  category          ExpenseCategory
  amount            Decimal           @db.Decimal(10, 2)
  currency          String            @default("BDT")
  expenseDate       DateTime          @db.Date
  paymentMethod     PaymentMethod
  vendor            String?
  receipt           String?           // File path
  status            ExpenseStatus     @default(PENDING)
  approvedBy        String?
  approvedAt        DateTime?
  paidAt            DateTime?
  notes             String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  center            Center            @relation(fields: [centerId], references: [id])
  centerId          String

  @@map("expenses")
}

model Purchase {
  id                String            @id @default(cuid())
  purchaseNumber    String            @unique
  vendor            String
  vendorEmail       String?
  vendorPhone       String?
  vendorAddress     String?
  purchaseDate      DateTime          @db.Date
  expectedDelivery  DateTime?         @db.Date
  actualDelivery    DateTime?         @db.Date
  subtotal          Decimal           @db.Decimal(10, 2)
  taxAmount         Decimal           @default(0) @db.Decimal(10, 2)
  discountAmount    Decimal           @default(0) @db.Decimal(10, 2)
  totalAmount       Decimal           @db.Decimal(10, 2)
  currency          String            @default("BDT")
  status            PurchaseStatus    @default(PENDING)
  notes             String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  center            Center            @relation(fields: [centerId], references: [id])
  centerId          String
  items             PurchaseItem[]

  @@map("purchases")
}

model PurchaseItem {
  id                String            @id @default(cuid())
  itemName          String
  description       String?
  quantity          Int
  unitPrice         Decimal           @db.Decimal(8, 2)
  totalPrice        Decimal           @db.Decimal(10, 2)
  receivedQuantity  Int               @default(0)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  purchase          Purchase          @relation(fields: [purchaseId], references: [id])
  purchaseId        String

  @@map("purchase_items")
}

model Invoice {
  id                String            @id @default(cuid())
  invoiceNumber     String            @unique
  clientName        String
  clientEmail       String?
  clientPhone       String?
  clientAddress     String?
  invoiceDate       DateTime          @db.Date
  dueDate           DateTime          @db.Date
  subtotal          Decimal           @db.Decimal(10, 2)
  taxAmount         Decimal           @default(0) @db.Decimal(10, 2)
  discountAmount    Decimal           @default(0) @db.Decimal(10, 2)
  totalAmount       Decimal           @db.Decimal(10, 2)
  paidAmount        Decimal           @default(0) @db.Decimal(10, 2)
  balanceAmount     Decimal           @db.Decimal(10, 2)
  currency          String            @default("BDT")
  status            InvoiceStatus     @default(DRAFT)
  notes             String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  center            Center            @relation(fields: [centerId], references: [id])
  centerId          String
  items             InvoiceItem[]
  payments          Payment[]

  @@map("invoices")
}

model InvoiceItem {
  id                String            @id @default(cuid())
  serviceName       String
  description       String?
  quantity          Int
  unitPrice         Decimal           @db.Decimal(8, 2)
  totalPrice        Decimal           @db.Decimal(10, 2)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  invoice           Invoice           @relation(fields: [invoiceId], references: [id])
  invoiceId         String

  @@map("invoice_items")
}

model Payment {
  id                String            @id @default(cuid())
  paymentNumber     String            @unique
  amount            Decimal           @db.Decimal(10, 2)
  currency          String            @default("BDT")
  paymentDate       DateTime          @db.Date
  paymentMethod     PaymentMethod
  reference         String?
  notes             String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  center            Center            @relation(fields: [centerId], references: [id])
  centerId          String
  invoice           Invoice?          @relation(fields: [invoiceId], references: [id])
  invoiceId         String?

  @@map("payments")
}
