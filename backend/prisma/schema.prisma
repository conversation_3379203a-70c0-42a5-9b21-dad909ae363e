// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                String   @id @default(cuid())
  firebaseUid       String   @unique
  email             String   @unique
  displayName       String?
  photoURL          String?
  fcmToken          String?
  role              UserRole @default(CENTER_OWNER)
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  ownedCenters      Center[]
  therapistProfile  Therapist?
  patientProfile    Patient?
  notifications     Notification[]

  @@map("users")
}

model Center {
  id                String            @id @default(cuid())
  name              String
  address           String
  phone             String?
  email             String?
  licenseNumber     String?
  licenseExpiry     DateTime?
  subscriptionPlan  SubscriptionPlan  @default(TRIAL)
  subscriptionStart DateTime          @default(now())
  subscriptionEnd   DateTime?
  isActive          Boolean           @default(true)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  owner             User              @relation(fields: [ownerId], references: [id])
  ownerId           String
  therapists        Therapist[]
  patients          Patient[]
  sessions          Session[]

  @@map("centers")
}

model Therapist {
  id                String   @id @default(cuid())
  specialization    String?
  licenseNumber     String?
  experience        Int?     // years of experience
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user              User     @relation(fields: [userId], references: [id])
  userId            String   @unique
  center            Center   @relation(fields: [centerId], references: [id])
  centerId          String
  sessions          Session[]

  @@map("therapists")
}

model Patient {
  id                String   @id @default(cuid())
  name              String
  age               Int?
  phone             String?
  address           String?
  medicalHistory    String?  @db.Text
  profilePhoto      String?
  emergencyContact  String?
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user              User?    @relation(fields: [userId], references: [id])
  userId            String?  @unique
  center            Center   @relation(fields: [centerId], references: [id])
  centerId          String
  sessions          Session[]

  @@map("patients")
}

model Session {
  id                String        @id @default(cuid())
  type              SessionType   @default(THERAPY)
  scheduledAt       DateTime
  duration          Int           // minutes
  status            SessionStatus @default(SCHEDULED)
  notes             String?       @db.Text
  painScoreBefore   Int?          // 1-10 scale
  painScoreAfter    Int?          // 1-10 scale
  progressNotes     String?       @db.Text
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  center            Center        @relation(fields: [centerId], references: [id])
  centerId          String
  patient           Patient       @relation(fields: [patientId], references: [id])
  patientId         String
  therapist         Therapist     @relation(fields: [therapistId], references: [id])
  therapistId       String

  @@map("sessions")
}

model Notification {
  id                String             @id @default(cuid())
  title             String
  body              String
  type              NotificationType   @default(REMINDER)
  isRead            Boolean            @default(false)
  scheduledFor      DateTime?
  sentAt            DateTime?
  createdAt         DateTime           @default(now())

  // Relations
  user              User               @relation(fields: [userId], references: [id])
  userId            String

  @@map("notifications")
}

enum UserRole {
  SUPER_ADMIN
  CENTER_OWNER
  THERAPIST
  PATIENT
}

enum SubscriptionPlan {
  TRIAL
  BASIC
  PRO
}

enum SessionType {
  CONSULTATION
  THERAPY
  FOLLOW_UP
}

enum SessionStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum NotificationType {
  REMINDER
  APPOINTMENT
  PAYMENT
  SYSTEM
}
