import Fastify from 'fastify';
import cors from '@fastify/cors';
import dotenv from 'dotenv';
import jwt from 'jsonwebtoken';

// Load environment variables
dotenv.config();

const fastify = Fastify({
  logger: true
});

// Register CORS
await fastify.register(cors, {
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
});

// Health check route
fastify.get('/health', async (request, reply) => {
  return { status: 'ok', timestamp: new Date().toISOString() };
});

// Admin login route
fastify.post('/api/auth/admin/login', async (request, reply) => {
  try {
    const { email, password } = request.body;
    
    console.log('Admin login attempt:', { email });
    
    // For testing, accept the demo credentials
    if (email === '<EMAIL>' && password === 'Admin@123456') {
      const token = jwt.sign({
        id: 'admin-1',
        email: email,
        role: 'admin',
        type: 'admin'
      }, process.env.JWT_SECRET || 'your-super-secret-jwt-key');
      
      return {
        success: true,
        message: 'Login successful',
        data: {
          token,
          admin: {
            id: 'admin-1',
            email: email,
            name: 'System Administrator',
            role: 'admin'
          }
        }
      };
    } else {
      return reply.code(401).send({
        success: false,
        message: 'Invalid credentials'
      });
    }
  } catch (error) {
    console.error('Admin login error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Firebase auth route (placeholder)
fastify.post('/api/auth/login', async (request, reply) => {
  try {
    const { firebaseToken, role } = request.body;
    
    console.log('Firebase login attempt:', { role });
    
    // For testing, return a mock response
    return reply.code(500).send({
      success: false,
      message: 'Firebase authentication not yet configured'
    });
  } catch (error) {
    console.error('Firebase login error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Start server
const start = async () => {
  try {
    await fastify.listen({ port: 3000, host: '0.0.0.0' });
    console.log('🚀 Test server running on http://localhost:3000');
    console.log('📋 Available routes:');
    console.log('  GET  /health');
    console.log('  POST /api/auth/admin/login');
    console.log('  POST /api/auth/login');
  } catch (err) {
    console.error('Error starting server:', err);
    process.exit(1);
  }
};

start();
