import Fastify from 'fastify';
import cors from '@fastify/cors';
import dotenv from 'dotenv';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';

// Load environment variables
dotenv.config();

const fastify = Fastify({
  logger: true
});

// Register CORS
await fastify.register(cors, {
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
});

// Health check route
fastify.get('/health', async (request, reply) => {
  return { status: 'ok', timestamp: new Date().toISOString() };
});

// Admin login route
fastify.post('/api/auth/admin/login', async (request, reply) => {
  try {
    const { email, password } = request.body;
    
    console.log('Admin login attempt:', { email });
    
    // For testing, accept the demo credentials
    if (email === '<EMAIL>' && password === 'Admin@123456') {
      const token = jwt.sign({
        id: 'admin-1',
        email: email,
        role: 'admin',
        type: 'admin'
      }, process.env.JWT_SECRET || 'your-super-secret-jwt-key');
      
      return {
        success: true,
        message: 'Login successful',
        data: {
          token,
          admin: {
            id: 'admin-1',
            email: email,
            name: 'System Administrator',
            role: 'admin'
          }
        }
      };
    } else {
      return reply.code(401).send({
        success: false,
        message: 'Invalid credentials'
      });
    }
  } catch (error) {
    console.error('Admin login error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Admin stats endpoint
fastify.get('/api/admin/stats', async (request, reply) => {
  try {
    // Mock admin stats data
    return {
      success: true,
      data: {
        totalCenters: 12,
        activeCenters: 10,
        totalUsers: 45,
        totalPatients: 234,
        totalSessions: 1567,
        recentCenters: [
          {
            id: '1',
            name: 'PhysioHealth Center',
            createdAt: new Date().toISOString(),
            owner: {
              email: '<EMAIL>',
              displayName: 'Dr. John Smith'
            }
          },
          {
            id: '2',
            name: 'Wellness Physiotherapy',
            createdAt: new Date().toISOString(),
            owner: {
              email: '<EMAIL>',
              displayName: 'Dr. Sarah Johnson'
            }
          }
        ]
      }
    };
  } catch (error) {
    console.error('Admin stats error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Admin centers endpoint
fastify.get('/api/admin/centers', async (request, reply) => {
  try {
    // Mock centers data
    return {
      success: true,
      data: [
        {
          id: '1',
          name: 'PhysioHealth Center',
          address: '123 Main St, Dhaka',
          phone: '+880-1234-567890',
          email: '<EMAIL>',
          subscriptionPlan: 'PRO',
          isActive: true,
          createdAt: new Date().toISOString(),
          owner: {
            id: 'owner1',
            email: '<EMAIL>',
            displayName: 'Dr. John Smith'
          }
        },
        {
          id: '2',
          name: 'Wellness Physiotherapy',
          address: '456 Park Ave, Chittagong',
          phone: '+880-1234-567891',
          email: '<EMAIL>',
          subscriptionPlan: 'BASIC',
          isActive: true,
          createdAt: new Date().toISOString(),
          owner: {
            id: 'owner2',
            email: '<EMAIL>',
            displayName: 'Dr. Sarah Johnson'
          }
        },
        {
          id: '3',
          name: 'Recovery Plus',
          address: '789 Health Blvd, Sylhet',
          phone: '+880-1234-567892',
          email: '<EMAIL>',
          subscriptionPlan: 'TRIAL',
          isActive: false,
          createdAt: new Date().toISOString(),
          owner: {
            id: 'owner3',
            email: '<EMAIL>',
            displayName: 'Dr. Ahmed Rahman'
          }
        }
      ]
    };
  } catch (error) {
    console.error('Admin centers error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Toggle center status
fastify.patch('/api/admin/centers/:id/status', async (request, reply) => {
  try {
    const { id } = request.params;
    const { isActive } = request.body;

    console.log(`Toggling center ${id} status to:`, isActive);

    // Mock response
    return {
      success: true,
      message: 'Center status updated successfully',
      data: {
        id,
        isActive,
        name: 'Mock Center',
        updatedAt: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error('Toggle center status error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Create new center
fastify.post('/api/admin/centers', async (request, reply) => {
  try {
    const centerData = request.body;
    console.log('Creating new center:', centerData.name);

    // Validate required fields
    if (!centerData.ownerPassword) {
      return reply.code(400).send({
        success: false,
        message: 'Owner password is required'
      });
    }

    // Hash the password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(centerData.ownerPassword, saltRounds);
    console.log('Password hashed successfully for owner:', centerData.ownerEmail);

    // Remove plain password from data before storing
    const { ownerPassword, confirmPassword, ...centerDataWithoutPassword } = centerData;

    // Mock response with hashed password stored securely
    return reply.code(201).send({
      success: true,
      message: 'Center created successfully',
      data: {
        id: 'new-center-' + Date.now(),
        ...centerDataWithoutPassword,
        isActive: true,
        createdAt: new Date().toISOString(),
        owner: {
          id: 'new-owner-' + Date.now(),
          email: centerData.ownerEmail,
          displayName: centerData.ownerName,
          hashedPassword: hashedPassword, // In real app, this would be stored in database
          authMethod: 'email_password'
        }
      }
    });
  } catch (error) {
    console.error('Create center error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Center Owner login endpoint
fastify.post('/api/auth/center-owner/login', async (request, reply) => {
  try {
    const { email, password } = request.body;

    console.log('Center Owner login attempt:', { email });

    // For testing, accept the demo credentials
    if (email === '<EMAIL>' && password === 'Owner@123456') {
      const token = jwt.sign({
        id: 'center-owner-1',
        email: email,
        role: 'center_owner',
        centerId: 'center-1',
        type: 'center_owner'
      }, process.env.JWT_SECRET || 'your-super-secret-jwt-key');

      return {
        success: true,
        message: 'Login successful',
        data: {
          token,
          centerOwner: {
            id: 'center-owner-1',
            email: email,
            name: 'Dr. John Smith',
            centerName: 'PhysioHealth Center',
            centerId: 'center-1',
            role: 'center_owner'
          }
        }
      };
    } else {
      return reply.code(401).send({
        success: false,
        message: 'Invalid credentials'
      });
    }
  } catch (error) {
    console.error('Center Owner login error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Center Owner stats endpoint
fastify.get('/api/center/stats', async (request, reply) => {
  try {
    // Mock center stats data
    return {
      success: true,
      data: {
        totalPatients: 156,
        todaySessions: 12,
        monthlyRevenue: 125000,
        activeTherapists: 8,
        weeklyGrowth: 15.2,
        completedSessions: 89,
        pendingSessions: 23
      }
    };
  } catch (error) {
    console.error('Center stats error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Center Owner recent sessions endpoint
fastify.get('/api/center/recent-sessions', async (request, reply) => {
  try {
    // Mock recent sessions data
    return {
      success: true,
      data: [
        {
          id: '1',
          patientName: 'Ahmed Hassan',
          therapistName: 'Dr. Sarah Ahmed',
          time: '10:30 AM',
          status: 'completed',
          date: new Date().toISOString()
        },
        {
          id: '2',
          patientName: 'Fatima Khan',
          therapistName: 'Dr. Rahman Ali',
          time: '11:15 AM',
          status: 'in-progress',
          date: new Date().toISOString()
        },
        {
          id: '3',
          patientName: 'Mohammad Rahman',
          therapistName: 'Dr. Nasir Uddin',
          time: '2:00 PM',
          status: 'scheduled',
          date: new Date().toISOString()
        },
        {
          id: '4',
          patientName: 'Rashida Begum',
          therapistName: 'Dr. Sarah Ahmed',
          time: '3:30 PM',
          status: 'completed',
          date: new Date().toISOString()
        },
        {
          id: '5',
          patientName: 'Karim Hossain',
          therapistName: 'Dr. Rahman Ali',
          time: '4:15 PM',
          status: 'scheduled',
          date: new Date().toISOString()
        }
      ]
    };
  } catch (error) {
    console.error('Recent sessions error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get center patients
fastify.get('/api/center/patients', async (request, reply) => {
  try {
    // Mock patients data
    return {
      success: true,
      data: [
        {
          id: 'patient-1',
          name: 'Ahmed Hassan',
          email: '<EMAIL>',
          phone: '+880-1234-567890',
          age: 35,
          gender: 'male',
          status: 'active',
          lastVisit: '2024-01-15',
          currentCondition: 'Lower back pain',
          address: '123 Main St, Dhaka',
          emergencyContact: 'Fatima Hassan',
          emergencyPhone: '+880-1234-567891'
        },
        {
          id: 'patient-2',
          name: 'Fatima Khan',
          email: '<EMAIL>',
          phone: '+880-1234-567892',
          age: 28,
          gender: 'female',
          status: 'active',
          lastVisit: '2024-01-14',
          currentCondition: 'Shoulder injury',
          address: '456 Park Ave, Chittagong',
          emergencyContact: 'Mohammad Khan',
          emergencyPhone: '+880-1234-567893'
        },
        {
          id: 'patient-3',
          name: 'Mohammad Rahman',
          email: '<EMAIL>',
          phone: '+880-1234-567894',
          age: 42,
          gender: 'male',
          status: 'completed',
          lastVisit: '2024-01-10',
          currentCondition: 'Knee rehabilitation',
          address: '789 Health Blvd, Sylhet',
          emergencyContact: 'Rashida Rahman',
          emergencyPhone: '+880-1234-567895'
        },
        {
          id: 'patient-4',
          name: 'Rashida Begum',
          email: '<EMAIL>',
          phone: '+880-1234-567896',
          age: 55,
          gender: 'female',
          status: 'active',
          lastVisit: '2024-01-12',
          currentCondition: 'Arthritis treatment',
          address: '321 Wellness St, Rajshahi',
          emergencyContact: 'Karim Begum',
          emergencyPhone: '+880-1234-567897'
        },
        {
          id: 'patient-5',
          name: 'Karim Hossain',
          email: '<EMAIL>',
          phone: '+880-1234-567898',
          age: 31,
          gender: 'male',
          status: 'inactive',
          lastVisit: '2023-12-20',
          currentCondition: 'Sports injury recovery',
          address: '654 Recovery Lane, Barisal',
          emergencyContact: 'Nasir Hossain',
          emergencyPhone: '+880-1234-567899'
        }
      ]
    };
  } catch (error) {
    console.error('Get patients error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Create new patient
fastify.post('/api/center/patients', async (request, reply) => {
  try {
    const patientData = request.body;
    console.log('Creating new patient:', patientData.name);

    // Mock response
    return reply.code(201).send({
      success: true,
      message: 'Patient created successfully',
      data: {
        id: 'patient-' + Date.now(),
        ...patientData,
        status: 'active',
        createdAt: new Date().toISOString(),
        lastVisit: null
      }
    });
  } catch (error) {
    console.error('Create patient error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get single patient
fastify.get('/api/center/patients/:id', async (request, reply) => {
  try {
    const { id } = request.params;
    console.log('Getting patient:', id);

    // Mock patient data
    return {
      success: true,
      data: {
        id: id,
        name: 'Ahmed Hassan',
        email: '<EMAIL>',
        phone: '+880-1234-567890',
        dateOfBirth: '1989-05-15',
        age: 35,
        gender: 'male',
        status: 'active',
        address: '123 Main St, Dhaka',
        emergencyContact: 'Fatima Hassan',
        emergencyPhone: '+880-1234-567891',
        currentCondition: 'Lower back pain',
        medicalHistory: 'Previous back surgery in 2020',
        referredBy: 'Dr. Smith',
        notes: 'Patient is very cooperative',
        createdAt: '2024-01-01T00:00:00.000Z',
        lastVisit: '2024-01-15'
      }
    };
  } catch (error) {
    console.error('Get patient error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update patient
fastify.put('/api/center/patients/:id', async (request, reply) => {
  try {
    const { id } = request.params;
    const patientData = request.body;
    console.log('Updating patient:', id);

    // Mock response
    return {
      success: true,
      message: 'Patient updated successfully',
      data: {
        id: id,
        ...patientData,
        updatedAt: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error('Update patient error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Delete patient
fastify.delete('/api/center/patients/:id', async (request, reply) => {
  try {
    const { id } = request.params;
    console.log('Deleting patient:', id);

    // Mock response
    return {
      success: true,
      message: 'Patient deleted successfully'
    };
  } catch (error) {
    console.error('Delete patient error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get center therapists
fastify.get('/api/center/therapists', async (request, reply) => {
  try {
    // Mock therapists data
    return {
      success: true,
      data: [
        {
          id: 'therapist-1',
          name: 'Dr. Sarah Ahmed',
          specialization: 'Physical Therapy',
          email: '<EMAIL>',
          phone: '+880-1234-567800',
          experience: '8 years',
          qualifications: 'DPT, MSc Physical Therapy',
          isActive: true
        },
        {
          id: 'therapist-2',
          name: 'Dr. Rahman Ali',
          specialization: 'Occupational Therapy',
          email: '<EMAIL>',
          phone: '+880-1234-567801',
          experience: '6 years',
          qualifications: 'MOT, BSc Occupational Therapy',
          isActive: true
        },
        {
          id: 'therapist-3',
          name: 'Dr. Nasir Uddin',
          specialization: 'Manual Therapy',
          email: '<EMAIL>',
          phone: '+880-1234-567802',
          experience: '10 years',
          qualifications: 'DPT, Certified Manual Therapist',
          isActive: true
        },
        {
          id: 'therapist-4',
          name: 'Dr. Fatima Rahman',
          specialization: 'Sports Therapy',
          email: '<EMAIL>',
          phone: '+880-1234-567803',
          experience: '5 years',
          qualifications: 'MSc Sports Therapy, BSc Physiotherapy',
          isActive: true
        }
      ]
    };
  } catch (error) {
    console.error('Get therapists error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get center sessions
fastify.get('/api/center/sessions', async (request, reply) => {
  try {
    // Mock sessions data
    return {
      success: true,
      data: [
        {
          id: 'session-1',
          patientId: 'patient-1',
          patientName: 'Ahmed Hassan',
          therapistId: 'therapist-1',
          therapistName: 'Dr. Sarah Ahmed',
          sessionType: 'Physical Therapy',
          date: '2024-01-16',
          time: '10:30',
          duration: 60,
          status: 'scheduled',
          treatmentPlan: 'Lower back strengthening exercises',
          goals: 'Reduce pain and improve mobility',
          notes: 'Patient reports improvement since last session'
        },
        {
          id: 'session-2',
          patientId: 'patient-2',
          patientName: 'Fatima Khan',
          therapistId: 'therapist-2',
          therapistName: 'Dr. Rahman Ali',
          sessionType: 'Occupational Therapy',
          date: '2024-01-16',
          time: '11:15',
          duration: 45,
          status: 'in-progress',
          treatmentPlan: 'Shoulder mobility exercises',
          goals: 'Restore range of motion',
          notes: 'Focus on daily living activities'
        },
        {
          id: 'session-3',
          patientId: 'patient-3',
          patientName: 'Mohammad Rahman',
          therapistId: 'therapist-3',
          therapistName: 'Dr. Nasir Uddin',
          sessionType: 'Manual Therapy',
          date: '2024-01-16',
          time: '14:00',
          duration: 60,
          status: 'scheduled',
          treatmentPlan: 'Knee joint mobilization',
          goals: 'Improve knee flexion',
          notes: 'Post-surgery rehabilitation'
        },
        {
          id: 'session-4',
          patientId: 'patient-1',
          patientName: 'Ahmed Hassan',
          therapistId: 'therapist-1',
          therapistName: 'Dr. Sarah Ahmed',
          sessionType: 'Exercise Therapy',
          date: '2024-01-15',
          time: '15:30',
          duration: 60,
          status: 'completed',
          treatmentPlan: 'Core strengthening program',
          goals: 'Build core stability',
          notes: 'Excellent progress, continue current plan'
        },
        {
          id: 'session-5',
          patientId: 'patient-5',
          patientName: 'Karim Hossain',
          therapistId: 'therapist-4',
          therapistName: 'Dr. Fatima Rahman',
          sessionType: 'Sports Therapy',
          date: '2024-01-14',
          time: '16:15',
          duration: 90,
          status: 'cancelled',
          treatmentPlan: 'Sports injury rehabilitation',
          goals: 'Return to sports activities',
          notes: 'Patient cancelled due to illness'
        }
      ]
    };
  } catch (error) {
    console.error('Get sessions error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Create new session
fastify.post('/api/center/sessions', async (request, reply) => {
  try {
    const sessionData = request.body;
    console.log('Creating new session for patient:', sessionData.patientId);

    // Mock response
    return reply.code(201).send({
      success: true,
      message: 'Session scheduled successfully',
      data: {
        id: 'session-' + Date.now(),
        ...sessionData,
        status: 'scheduled',
        createdAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Create session error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update session status
fastify.patch('/api/center/sessions/:id/status', async (request, reply) => {
  try {
    const { id } = request.params;
    const { status } = request.body;
    console.log(`Updating session ${id} status to:`, status);

    // Mock response
    return {
      success: true,
      message: 'Session status updated successfully',
      data: {
        id,
        status,
        updatedAt: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error('Update session status error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Delete session
fastify.delete('/api/center/sessions/:id', async (request, reply) => {
  try {
    const { id } = request.params;
    console.log('Deleting session:', id);

    // Mock response
    return {
      success: true,
      message: 'Session deleted successfully'
    };
  } catch (error) {
    console.error('Delete session error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Firebase auth route (placeholder)
fastify.post('/api/auth/login', async (request, reply) => {
  try {
    const { firebaseToken, role } = request.body;

    console.log('Firebase login attempt:', { role });

    // For testing, return a mock response
    return reply.code(500).send({
      success: false,
      message: 'Firebase authentication not yet configured'
    });
  } catch (error) {
    console.error('Firebase login error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Start server
const start = async () => {
  try {
    await fastify.listen({ port: 3000, host: '0.0.0.0' });
    console.log('🚀 Test server running on http://localhost:3000');
    console.log('📋 Available routes:');
    console.log('  GET  /health');
    console.log('  POST /api/auth/admin/login');
    console.log('  GET  /api/admin/stats');
    console.log('  GET  /api/admin/centers');
    console.log('  PATCH /api/admin/centers/:id/status');
    console.log('  POST /api/admin/centers');
    console.log('  POST /api/auth/center-owner/login');
    console.log('  GET  /api/center/stats');
    console.log('  GET  /api/center/recent-sessions');
    console.log('  GET  /api/center/patients');
    console.log('  POST /api/center/patients');
    console.log('  GET  /api/center/patients/:id');
    console.log('  PUT  /api/center/patients/:id');
    console.log('  DELETE /api/center/patients/:id');
    console.log('  GET  /api/center/therapists');
    console.log('  GET  /api/center/sessions');
    console.log('  POST /api/center/sessions');
    console.log('  PATCH /api/center/sessions/:id/status');
    console.log('  DELETE /api/center/sessions/:id');
    console.log('  POST /api/auth/login');
  } catch (err) {
    console.error('Error starting server:', err);
    process.exit(1);
  }
};

start();
