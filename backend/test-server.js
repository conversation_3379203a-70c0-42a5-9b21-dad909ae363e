import Fastify from 'fastify';
import cors from '@fastify/cors';
import dotenv from 'dotenv';
import jwt from 'jsonwebtoken';

// Load environment variables
dotenv.config();

const fastify = Fastify({
  logger: true
});

// Register CORS
await fastify.register(cors, {
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
});

// Health check route
fastify.get('/health', async (request, reply) => {
  return { status: 'ok', timestamp: new Date().toISOString() };
});

// Admin login route
fastify.post('/api/auth/admin/login', async (request, reply) => {
  try {
    const { email, password } = request.body;
    
    console.log('Admin login attempt:', { email });
    
    // For testing, accept the demo credentials
    if (email === '<EMAIL>' && password === 'Admin@123456') {
      const token = jwt.sign({
        id: 'admin-1',
        email: email,
        role: 'admin',
        type: 'admin'
      }, process.env.JWT_SECRET || 'your-super-secret-jwt-key');
      
      return {
        success: true,
        message: 'Login successful',
        data: {
          token,
          admin: {
            id: 'admin-1',
            email: email,
            name: 'System Administrator',
            role: 'admin'
          }
        }
      };
    } else {
      return reply.code(401).send({
        success: false,
        message: 'Invalid credentials'
      });
    }
  } catch (error) {
    console.error('Admin login error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Admin stats endpoint
fastify.get('/api/admin/stats', async (request, reply) => {
  try {
    // Mock admin stats data
    return {
      success: true,
      data: {
        totalCenters: 12,
        activeCenters: 10,
        totalUsers: 45,
        totalPatients: 234,
        totalSessions: 1567,
        recentCenters: [
          {
            id: '1',
            name: 'PhysioHealth Center',
            createdAt: new Date().toISOString(),
            owner: {
              email: '<EMAIL>',
              displayName: 'Dr. John Smith'
            }
          },
          {
            id: '2',
            name: 'Wellness Physiotherapy',
            createdAt: new Date().toISOString(),
            owner: {
              email: '<EMAIL>',
              displayName: 'Dr. Sarah Johnson'
            }
          }
        ]
      }
    };
  } catch (error) {
    console.error('Admin stats error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Admin centers endpoint
fastify.get('/api/admin/centers', async (request, reply) => {
  try {
    // Mock centers data
    return {
      success: true,
      data: [
        {
          id: '1',
          name: 'PhysioHealth Center',
          address: '123 Main St, Dhaka',
          phone: '+880-1234-567890',
          email: '<EMAIL>',
          subscriptionPlan: 'PRO',
          isActive: true,
          createdAt: new Date().toISOString(),
          owner: {
            id: 'owner1',
            email: '<EMAIL>',
            displayName: 'Dr. John Smith'
          }
        },
        {
          id: '2',
          name: 'Wellness Physiotherapy',
          address: '456 Park Ave, Chittagong',
          phone: '+880-1234-567891',
          email: '<EMAIL>',
          subscriptionPlan: 'BASIC',
          isActive: true,
          createdAt: new Date().toISOString(),
          owner: {
            id: 'owner2',
            email: '<EMAIL>',
            displayName: 'Dr. Sarah Johnson'
          }
        },
        {
          id: '3',
          name: 'Recovery Plus',
          address: '789 Health Blvd, Sylhet',
          phone: '+880-1234-567892',
          email: '<EMAIL>',
          subscriptionPlan: 'TRIAL',
          isActive: false,
          createdAt: new Date().toISOString(),
          owner: {
            id: 'owner3',
            email: '<EMAIL>',
            displayName: 'Dr. Ahmed Rahman'
          }
        }
      ]
    };
  } catch (error) {
    console.error('Admin centers error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Toggle center status
fastify.patch('/api/admin/centers/:id/status', async (request, reply) => {
  try {
    const { id } = request.params;
    const { isActive } = request.body;

    console.log(`Toggling center ${id} status to:`, isActive);

    // Mock response
    return {
      success: true,
      message: 'Center status updated successfully',
      data: {
        id,
        isActive,
        name: 'Mock Center',
        updatedAt: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error('Toggle center status error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Create new center
fastify.post('/api/admin/centers', async (request, reply) => {
  try {
    const centerData = request.body;
    console.log('Creating new center:', centerData);

    // Mock response
    return reply.code(201).send({
      success: true,
      message: 'Center created successfully',
      data: {
        id: 'new-center-' + Date.now(),
        ...centerData,
        isActive: true,
        createdAt: new Date().toISOString(),
        owner: {
          id: 'new-owner',
          email: centerData.ownerEmail,
          displayName: centerData.ownerName
        }
      }
    });
  } catch (error) {
    console.error('Create center error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Center Owner login endpoint
fastify.post('/api/auth/center-owner/login', async (request, reply) => {
  try {
    const { email, password } = request.body;

    console.log('Center Owner login attempt:', { email });

    // For testing, accept the demo credentials
    if (email === '<EMAIL>' && password === 'Owner@123456') {
      const token = jwt.sign({
        id: 'center-owner-1',
        email: email,
        role: 'center_owner',
        centerId: 'center-1',
        type: 'center_owner'
      }, process.env.JWT_SECRET || 'your-super-secret-jwt-key');

      return {
        success: true,
        message: 'Login successful',
        data: {
          token,
          centerOwner: {
            id: 'center-owner-1',
            email: email,
            name: 'Dr. John Smith',
            centerName: 'PhysioHealth Center',
            centerId: 'center-1',
            role: 'center_owner'
          }
        }
      };
    } else {
      return reply.code(401).send({
        success: false,
        message: 'Invalid credentials'
      });
    }
  } catch (error) {
    console.error('Center Owner login error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Center Owner stats endpoint
fastify.get('/api/center/stats', async (request, reply) => {
  try {
    // Mock center stats data
    return {
      success: true,
      data: {
        totalPatients: 156,
        todaySessions: 12,
        monthlyRevenue: 125000,
        activeTherapists: 8,
        weeklyGrowth: 15.2,
        completedSessions: 89,
        pendingSessions: 23
      }
    };
  } catch (error) {
    console.error('Center stats error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Center Owner recent sessions endpoint
fastify.get('/api/center/recent-sessions', async (request, reply) => {
  try {
    // Mock recent sessions data
    return {
      success: true,
      data: [
        {
          id: '1',
          patientName: 'Ahmed Hassan',
          therapistName: 'Dr. Sarah Ahmed',
          time: '10:30 AM',
          status: 'completed',
          date: new Date().toISOString()
        },
        {
          id: '2',
          patientName: 'Fatima Khan',
          therapistName: 'Dr. Rahman Ali',
          time: '11:15 AM',
          status: 'in-progress',
          date: new Date().toISOString()
        },
        {
          id: '3',
          patientName: 'Mohammad Rahman',
          therapistName: 'Dr. Nasir Uddin',
          time: '2:00 PM',
          status: 'scheduled',
          date: new Date().toISOString()
        },
        {
          id: '4',
          patientName: 'Rashida Begum',
          therapistName: 'Dr. Sarah Ahmed',
          time: '3:30 PM',
          status: 'completed',
          date: new Date().toISOString()
        },
        {
          id: '5',
          patientName: 'Karim Hossain',
          therapistName: 'Dr. Rahman Ali',
          time: '4:15 PM',
          status: 'scheduled',
          date: new Date().toISOString()
        }
      ]
    };
  } catch (error) {
    console.error('Recent sessions error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Firebase auth route (placeholder)
fastify.post('/api/auth/login', async (request, reply) => {
  try {
    const { firebaseToken, role } = request.body;

    console.log('Firebase login attempt:', { role });

    // For testing, return a mock response
    return reply.code(500).send({
      success: false,
      message: 'Firebase authentication not yet configured'
    });
  } catch (error) {
    console.error('Firebase login error:', error);
    return reply.code(500).send({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Start server
const start = async () => {
  try {
    await fastify.listen({ port: 3000, host: '0.0.0.0' });
    console.log('🚀 Test server running on http://localhost:3000');
    console.log('📋 Available routes:');
    console.log('  GET  /health');
    console.log('  POST /api/auth/admin/login');
    console.log('  GET  /api/admin/stats');
    console.log('  GET  /api/admin/centers');
    console.log('  PATCH /api/admin/centers/:id/status');
    console.log('  POST /api/admin/centers');
    console.log('  POST /api/auth/center-owner/login');
    console.log('  GET  /api/center/stats');
    console.log('  GET  /api/center/recent-sessions');
    console.log('  POST /api/auth/login');
  } catch (err) {
    console.error('Error starting server:', err);
    process.exit(1);
  }
};

start();
