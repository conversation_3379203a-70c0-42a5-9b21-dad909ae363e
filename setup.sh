#!/bin/bash

# PhysioCenter SaaS Setup Script
# This script sets up the entire project from scratch

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_header "🔍 Checking Prerequisites..."
    
    # Check if Bun is installed
    if ! command -v bun &> /dev/null; then
        print_error "Bun.js is not installed. Please install it from https://bun.sh/"
        exit 1
    fi
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install it from https://nodejs.org/"
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install Node.js which includes npm."
        exit 1
    fi
    
    # Check if MySQL is running (optional check)
    if command -v mysql &> /dev/null; then
        print_success "MySQL found"
    else
        print_warning "MySQL not found. Make sure you have a MySQL database available."
    fi
    
    print_success "Prerequisites check completed"
}

# Setup environment files
setup_environment() {
    print_header "📝 Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        if [ -f "backend/.env.example" ]; then
            cp backend/.env.example backend/.env
            print_success "Created backend/.env from example"
            print_warning "Please edit backend/.env with your database and Firebase configuration"
        else
            print_error "backend/.env.example not found"
        fi
    else
        print_success "backend/.env already exists"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ]; then
        if [ -f "frontend/.env.example" ]; then
            cp frontend/.env.example frontend/.env
            print_success "Created frontend/.env from example"
            print_warning "Please edit frontend/.env with your Firebase configuration"
        else
            print_error "frontend/.env.example not found"
        fi
    else
        print_success "frontend/.env already exists"
    fi
}

# Install dependencies
install_dependencies() {
    print_header "📦 Installing dependencies..."
    
    # Backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    bun install
    cd ..
    print_success "Backend dependencies installed"
    
    # Frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    print_success "Frontend dependencies installed"
    
    # Test dependencies
    print_status "Installing test dependencies..."
    cd tests
    npm install
    cd ..
    print_success "Test dependencies installed"
}

# Setup database
setup_database() {
    print_header "🗄️ Setting up database..."
    
    cd backend
    
    # Generate Prisma client
    print_status "Generating Prisma client..."
    bunx prisma generate
    
    print_warning "Database schema is ready. To complete setup:"
    print_warning "1. Configure your database connection in backend/.env"
    print_warning "2. Run 'cd backend && bun run setup:dev' to push schema and seed data"
    
    cd ..
}

# Setup Playwright
setup_playwright() {
    print_header "🎭 Setting up Playwright..."
    
    cd tests
    print_status "Installing Playwright browsers..."
    npx playwright install
    cd ..
    
    print_success "Playwright setup complete"
}

# Display next steps
show_next_steps() {
    print_header "🎉 Setup Complete!"
    echo
    print_header "📋 Next Steps:"
    echo
    print_status "1. Configure your environment files:"
    echo "   - Edit backend/.env with your database and Firebase configuration"
    echo "   - Edit frontend/.env with your Firebase configuration"
    echo
    print_status "2. Set up your database:"
    echo "   cd backend && bun run setup:dev"
    echo
    print_status "3. Start development servers:"
    echo "   # Terminal 1 - Backend"
    echo "   cd backend && bun run dev"
    echo
    echo "   # Terminal 2 - Frontend"
    echo "   cd frontend && npm run dev"
    echo
    print_status "4. Run tests:"
    echo "   node scripts/test-runner.js"
    echo
    print_header "🌐 URLs:"
    echo "   - Frontend: http://localhost:5173"
    echo "   - Backend API: http://localhost:3000"
    echo "   - API Docs: http://localhost:3000/docs"
    echo
    print_header "🔑 Test Accounts (after seeding):"
    echo "   - Super Admin: <EMAIL>"
    echo "   - Various center owners, therapists, and patients"
    echo
    print_header "💡 Tips:"
    echo "   - Use 'bun run db:studio' to view database in browser"
    echo "   - Check logs for any configuration issues"
    echo "   - Refer to subscription plans in README.md"
    echo
    print_success "Happy coding! 🚀"
}

# Main execution
main() {
    print_header "🚀 PhysioCenter SaaS Setup"
    print_header "=========================="
    echo
    
    check_prerequisites
    echo
    
    setup_environment
    echo
    
    install_dependencies
    echo
    
    setup_database
    echo
    
    setup_playwright
    echo
    
    show_next_steps
}

# Run main function
main
